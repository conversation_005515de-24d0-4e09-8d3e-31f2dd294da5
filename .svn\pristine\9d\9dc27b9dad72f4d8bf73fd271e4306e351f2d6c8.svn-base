using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormViewImage : BaseForm
    {

        private UcContent ucContent;

        // 记录窗口尺寸状态
        private Size _lastNormalSize;
        private bool _sizeChangeInProgress = false;

        public FormViewImage()
        {
            Icon = FrmMain.FrmTool.Icon;
            Text = $"{"图像预览".CurrentText()}-{CommonString.FullName.CurrentText()}";
            ShowIcon = true;
            ShowInTaskbar = true;

            FormBorderStyle = FormBorderStyle.Sizable;
            WindowState = FormWindowState.Normal;
            StartPosition = FormStartPosition.Manual;

            ucContent = new UcContent(true)
            {
                Dock = DockStyle.Fill,
                IsShowOldContent = false,
                IsShowToolBox = true,
                IsShowTxt = false,
                Margin = CommonString.PaddingZero,
                Padding = CommonString.PaddingZero,
                Location = new Point(0, 0),
                Name = "ucContent",
                TabIndex = 0,
                TabStop = false,
                ScalingStrategy = ImageBox.ImageScalingStrategy.FitClear
            };
            Controls.Add(ucContent);
            InitializeComponent();
            Shown += FormViewImage_Shown;
            FormClosing += FormViewImage_FormClosing;
        }

        private void FormViewImage_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                ucContent.SetImageZoomSmall();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"FormViewImage_FormClosing error: {ex.Message}");
            }
            finally
            {
                MemoryManager.ClearMemory();
            }
        }

        private void FormViewImage_Shown(object sender, EventArgs e)
        {
            Application.DoEvents();
            CommonMethod.DetermineCallAsync(ucContent, () => ucContent.SetImageZoomBig(true));
            SizeChanged += FormViewImage_SizeChanged;
            this.ForceActivate();
        }

        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000;
                return cp;
            }
        }

        private void FormViewImage_SizeChanged(object sender, EventArgs e)
        {
            FormBorderStyle = WindowState == FormWindowState.Maximized ? FormBorderStyle.None : FormBorderStyle.Sizable;
            if (WindowState == FormWindowState.Normal && !_sizeChangeInProgress)
            {
                if (_lastNormalSize != Size)
                {
                    SetImageMode(ucContent.Image);
                    _lastNormalSize = Size;
                }
            }
        }

        private void SetImageMode(Image image, bool isInit = false)
        {
            if (image == null) return;

            try
            {
                _sizeChangeInProgress = true;
                Size = ImageProcessHelper.CalculatePicasaWindowSize(this, image.Size);
                _lastNormalSize = Size;

                Location = new Point((Screen.PrimaryScreen.WorkingArea.Width - Size.Width) / 2, (Screen.PrimaryScreen.WorkingArea.Height - Size.Height) / 2);
            }
            finally
            {
                _sizeChangeInProgress = false;
            }
        }

        internal void Bind(Image image, string fileName = null)
        {
            try
            {
                if (image == null && !string.IsNullOrEmpty(fileName))
                {
                    try
                    {
                        image = Image.FromFile(fileName);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error loading image from file: {ex.Message}");
                        return;
                    }
                }

                if (WindowState == FormWindowState.Maximized)
                {
                    WindowState = FormWindowState.Normal;
                }

                Text = $"{"图像预览".CurrentText()} {"尺寸".CurrentText()}:{image?.Width}×{image?.Height} - {CommonString.FullName.CurrentText()}";

                ucContent.BindImage(image, true, CommonSetting.图片自动缩放);
                ucContent.NowDisplayMode = DisplayModel.图文模式;
                ucContent.RefreshStyle();
                ucContent.BindContentByOcr(FrmMain.EmptyContent);
                ucContent.SetCanClose();

                _lastNormalSize = Size.Empty;
                _sizeChangeInProgress = false;

                SetImageMode(ucContent.Image, true);
                BackColor = ucContent.ImageBoxBackColor;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"FormViewImage.Bind error: {ex.Message}");
            }
        }

        private void FrmPicCompare_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) Close();
        }

        private void FormViewImage_Load(object sender, EventArgs e)
        {
            ucContent.ShowImageTool();
        }
    }
}
