using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FormViewImage : BaseForm
    {

        private UcContent ucContent;

        // 记录窗口尺寸状态
        private Size _lastNormalSize;
        private bool _sizeChangeInProgress = false;

        // 图文比对模式相关属性
        private bool _isCompareMode = false;
        private bool _isShowOldContent = false;

        /// <summary>
        /// 默认构造函数 - 纯图像预览模式
        /// </summary>
        public FormViewImage() : this(false)
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="isCompareMode">是否为图文比对模式</param>
        public FormViewImage(bool isCompareMode)
        {
            _isCompareMode = isCompareMode;

            Icon = FrmMain.FrmTool.Icon;

            FormBorderStyle = FormBorderStyle.Sizable;
            WindowState = FormWindowState.Normal;
            StartPosition = FormStartPosition.Manual;

            ShowIcon = true;
            ShowInTaskbar = true;

            // 注意：图文比对模式下不传递isImageMode参数（默认false），纯图像预览模式传递true
            ucContent = new UcContent(_isCompareMode ? false : true)
            {
                Dock = DockStyle.Fill,
                IsShowOldContent = _isCompareMode ? _isShowOldContent : false,
                IsShowToolBox = true,
                IsShowTxt = _isCompareMode,
                Margin = CommonString.PaddingZero,
                Padding = CommonString.PaddingZero,
                Location = new Point(0, 0),
                Name = "ucContent",
                TabIndex = 0,
                TabStop = false,
            };
            Controls.Add(ucContent);
            InitializeComponent();
            Shown += FormViewImage_Shown;
            FormClosing += FormViewImage_FormClosing;
        }

        private void FormViewImage_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                ucContent.SetImageZoomSmall();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"FormViewImage_FormClosing error: {ex.Message}");
            }
            finally
            {
                MemoryManager.ClearMemory();
            }
        }

        private void FrmPicCompare_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) Close();
        }

        private void FormViewImage_Load(object sender, EventArgs e)
        {
            if (_isCompareMode)
                Text = $"{ucContent?.NowDisplayMode.ToString()?.CurrentText()}-【{ucContent?.OcrContent?.processName?.CurrentText()}】";
            else
                Text = $"{"图像预览".CurrentText()} {"尺寸".CurrentText()}:{ucContent?.Image?.Width}×{ucContent?.Image?.Height} - {CommonString.FullName.CurrentText()}";

            ucContent.ShowImageTool();
        }

        private void FormViewImage_Shown(object sender, EventArgs e)
        {
            Application.DoEvents();
            CommonMethod.DetermineCallAsync(ucContent, () => ucContent.SetImageZoomBig(true));
            SizeChanged += FormViewImage_SizeChanged;
            this.ForceActivate();
        }

        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000;
                return cp;
            }
        }

        private void FormViewImage_SizeChanged(object sender, EventArgs e)
        {
            FormBorderStyle = WindowState == FormWindowState.Maximized ? FormBorderStyle.None : FormBorderStyle.Sizable;
            if (WindowState == FormWindowState.Normal && !_sizeChangeInProgress)
            {
                if (_lastNormalSize != Size)
                {
                    SetImageMode(ucContent.Image);
                    _lastNormalSize = Size;
                }
            }
        }

        private void SetImageMode(Image image)
        {
            if (image == null) return;

            try
            {
                _sizeChangeInProgress = true;
                Size = ImageProcessHelper.CalculatePicasaWindowSize(this, image.Size);
                _lastNormalSize = Size;

                Location = new Point((Screen.PrimaryScreen.WorkingArea.Width - Size.Width) / 2, (Screen.PrimaryScreen.WorkingArea.Height - Size.Height) / 2);
            }
            finally
            {
                _sizeChangeInProgress = false;
            }
        }

        /// <summary>
        /// 初始化图文比对模式的参数
        /// </summary>
        /// <param name="spiltMode">分段模式</param>
        /// <param name="isShowOld">是否显示旧内容</param>
        internal void Init(SpiltMode spiltMode, bool isShowOld)
        {
            if (!_isCompareMode) return;

            _isShowOldContent = isShowOld;
            ucContent.SpiltModel = spiltMode;
            ucContent.IsShowOldContent = isShowOld;
        }

        /// <summary>
        /// 统一的图像绑定方法
        /// </summary>
        /// <param name="image">图像</param>
        /// <param name="fileName">文件名（可选，用于从文件加载图像）</param>
        /// <param name="ocrContent">OCR内容（可选，图文比对模式使用）</param>
        /// <param name="displayModel">显示模式（可选，图文比对模式使用）</param>
        internal void Bind(Image image, string fileName = null, OcrContent ocrContent = null, DisplayModel? displayModel = null)
        {
            try
            {
                if (image == null && !string.IsNullOrEmpty(fileName))
                {
                    try
                    {
                        image = Image.FromFile(fileName);
                    }
                    catch (Exception ex)
                    {
                        return;
                    }
                }

                if (image == null)
                {
                    return;
                }

                ucContent.BindImage(image, true, CommonSetting.图片自动缩放);

                if (_isCompareMode && displayModel.HasValue)
                {
                    ucContent.NowDisplayMode = displayModel.Value;
                }
                else
                {
                    ucContent.NowDisplayMode = DisplayModel.图文模式;
                }

                ucContent.RefreshStyle();

                ucContent.BindContentByOcr(ocrContent != null ? ocrContent : FrmMain.EmptyContent);

                ucContent.SetCanClose();

                _lastNormalSize = Size.Empty;
                _sizeChangeInProgress = false;

                SetImageMode(image);
                BackColor = ucContent.ImageBoxBackColor;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"FormViewImage.Bind error: {ex.Message}");
            }
        }
    }
}
