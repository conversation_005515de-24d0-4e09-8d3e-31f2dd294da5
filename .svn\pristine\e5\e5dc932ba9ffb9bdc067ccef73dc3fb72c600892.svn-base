﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace OCRTools
{
    public partial class FrmPicCompare : MetroForm
    {
        public FrmPicCompare()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;
            Load += FrmPicCompare_Load;
            Shown += FormViewImage_Shown;
            FormClosing += FrmPicCompare_FormClosing;
        }

        private void FrmPicCompare_Load(object sender, EventArgs e)
        {
            content.ShowImageTool();
        }

        private void FormViewImage_Shown(object sender, EventArgs e)
        {
            Application.DoEvents();
            CommonMethod.DetermineCallAsync(content, () => content.SetImageZoomBig(true));
            this.ForceActivate();
        }

        private void FrmPicCompare_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                content.SetImageZoomSmall();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"FormViewImage_FormClosing error: {ex.Message}");
            }
            finally
            {
                MemoryManager.ClearMemory();
            }
        }

        internal void Init(SpiltMode spiltMode, bool isShowOld)
        {
            content.SpiltModel = spiltMode;
            content.IsShowOldContent = isShowOld;
        }

        internal void Bind(Image image, OcrContent ocrContent, DisplayModel displayModel)
        {
            Size = ImageProcessHelper.CalculatePicasaWindowSize(this, image.Size);
            content.ScalingStrategy = ImageBox.ImageScalingStrategy.FitComplete;
            content.BindImage(image, true, CommonSetting.图片自动缩放);
            content.NowDisplayMode = displayModel;
            if (ocrContent != null)
            {
                content.RefreshStyle();
                content.BindContentByOcr(ocrContent);
                Text = $"{content.NowDisplayMode.ToString().CurrentText()}-【{ocrContent.processName.CurrentText()}】";
            }
        }

        private void FrmPicCompare_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape) Close();
        }
    }
}