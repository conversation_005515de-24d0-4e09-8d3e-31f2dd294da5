using ImageLib;
using MetroFramework.Forms;
using O2S.Components.PDFRender4NET;
using OCRTools.Common;
using OCRTools.Common.Hook;
using OCRTools.Language;
using OCRTools.NewForms;
using OCRTools.Properties;
using OCRTools.ScreenCaptureLib;
using OCRTools.Shadow;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Windows.Automation;
using System.Windows.Forms;
using static ImageBox;

namespace OCRTools
{
    public partial class FrmMain : MetroForm
    {
        private string StrProcessOcr = "正在识别";
        private string StrToLoginStr = "登录";

        private const int MinFixAreaWidth = 10;

        public static FormTool FrmTool = new FormTool { IsImage = true };

        public void ShowNotifyMain(bool visible = true)
        {
            notifyMain.Visible = visible;
        }

        public static FormUpdate FormUpdate;

        private static readonly int minWidth = 300;
        private static readonly int minHeight = 150;

        public static Action<List<string>, string, OcrType?, ProcessBy, string> DragDropEventDelegate;

        public static Action<OcrType?, ProcessBy, Image, string> RecByImageDelegate;

        public static Action ChangeThemeDelegate;

        public static Action LoginDelegate;

        public static List<SearchEngine> LstSearchEngine = new List<SearchEngine>();

        private static TransLanguageTypeEnum CurrentTranslateFrom;

        private static TransLanguageTypeEnum CurrentTranslateTo;

        private static OcrType NowOcrType;
        private static List<int> NowOcrGroupType = new List<int>() { 0 };

        private static bool IsFromLeftToRight = true;
        private static bool IsFromTopToDown = true;

        private readonly BlockingCollection<OcrContent> OcrResultPool = new BlockingCollection<OcrContent>();

        private SpiltMode NowSpiltModel;

        private bool IsCopyTrans;

        private bool IsOcrForSearch;

        private bool IsShowOldContent;

        private LoadingType nowLoadingType;

        private TabPage tbContentText;
        private UcContent content;

        private TabPage tbImageBox;

        public FrmMain()
        {
            DisplayHeader = false;
            Padding = new Padding(20, 35, 20, 20);
            CheckForIllegalCrossThreadCalls = false;

            InitializeComponent();

            ShadowType = CommonString.CommonShadowType;

            FormClosing += FrmMain_FormClosing;

            notifyMain.Icon = Icon;
            notifyMain.ContextMenuStrip = cmsNotify;
            ShowNotifyMain(CommonSetting.显示系统托盘);
            cmsNotify.Opening += CmsNotify_Opening;

            FrmTool.Icon = Icon;
            FrmTool.ContextMenuStrip = cmsNotify;
            FrmTool.MouseDoubleClick += FrmTool_MouseDoubleClick;
            FrmTool.SetWindowLong();

            DragDropEventDelegate = (lstStr, fileExt, ocrType, processBy, fileIdentity) =>
            {
                if (!ocrType.HasValue) ocrType = NowOcrType;
                var fileName = lstStr[0];
                ProcessFile(ocrType.Value, processBy, fileName, fileIdentity, fileExt);
            };
            RecByImageDelegate = (ocrType, processBy, img, url) =>
            {
                ProcessByImage(NowOcrType, processBy, img, false, url);
            };
            ChangeThemeDelegate = () =>
            {
                CommonSetting.SetValue("夜间模式", CommonSetting.夜间模式);
                CommonTheme.RefreshTheme();
                string strQuXiaoTop = "取消置顶".CurrentText();
                string strToTop = "置顶窗体".CurrentText();
                string strRiJain = "日间模式".CurrentText();
                string strYeJain = "夜间模式".CurrentText();
                tipMain.SetToolTip(topCtrl, TopMost ? strQuXiaoTop : strToTop);
                tipMain.SetToolTip(sunCtrl, CommonSetting.夜间模式 ? strRiJain : strYeJain);
                this.Invalidate();
            };
            LoginDelegate = () =>
            {
                using (var frmLogin = new FrmLogin())
                {
                    frmLogin.Icon = Icon;
                    frmLogin.ShowDialog(this);
                }
            };
            Program.NowUserChanged += (object sender, EventArgs e) =>
            {
                BindUserInfo();
            };

            tbMain.KeyDown += TxtContent_KeyDown;
            KeyDown += TxtContent_KeyDown;
            tsmShowOldContent.SetStyleManager(StyleManager);
            Application.ApplicationExit += Application_ApplicationExit;

            Opacity = 0;

            if (CommonSetting.记住窗口尺寸 && !CommonSetting.上次窗口尺寸.IsValidate())
            {
                try
                {
                    if (CommonSetting.上次窗口尺寸.Width > 300 && CommonSetting.上次窗口尺寸.Height > 300)
                        this.Size = CommonSetting.上次窗口尺寸;
                }
                catch { }
            }
        }

        private void FrmMain_Load(object sender, EventArgs e)
        {
            // 同步执行UI增强组件初始化（这些组件直接影响界面显示）
            InitCriticalUIComponents();

            // 启动异步初始化流程
            Task.Run(StartAsyncInitialization);
        }

        /// <summary>
        /// UI关键组件初始化 - 必须在UI线程同步执行
        /// </summary>
        internal void InitCriticalUIComponents()
        {
            this.SuspendLayout();

            try
            {
                FrmTool.TopMost = true;
                FrmTool.Visible = CommonSetting.显示工具栏;

                ucLoading1.InitLoading(tbMain.Size, tbMain.Location);
                ucLoading1.Anchor = tbMain.Anchor;
                InitContent();

                if (Enum.TryParse(CommonSetting.展示模式, out DisplayModel displayMode))
                {
                    NowDisplayMode = displayMode;
                }
                NowOcrGroupType = OcrHelper.GetGroupByName(CommonSetting.识别引擎);
                NowSpiltModel = CommonSetting.ConvertToEnum(CommonSetting.分段模式, NowSpiltModel);
                NowLoadingType = CommonSetting.ConvertToEnum(CommonSetting.加载动画, NowLoadingType);

                SetTopMost(CommonSetting.窗体置顶);

                // 合并所有需要延迟执行的UI操作到一个BeginInvoke中
                BeginInvoke(new Action(() =>
                {
                    try
                    {
                        // 初始化显示模式
                        InitDisplayModel();

                        // 创建内容控件
                        content = new UcContent
                        {
                            Dock = DockStyle.Fill,
                            SpiltModel = NowSpiltModel,
                            IsShowOldContent = IsShowOldContent,
                            MenuStrip = cmsNotify,
                            TxtKeyDownEventDelegate = TxtContent_KeyDown,
                            ScalingStrategy = ImageScalingStrategy.FitInteractive
                        };
                        if (tbContentText != null)
                        {
                            tbContentText.Controls.Add(content);
                        }
                        content.SetDragDrop();

                        // 设置拖放功能
                        try
                        {
                            this.ControlUseDrop();
                            tbMain.ControlUseDrop();
                            FrmTool.SetDragDrop();
                        }
                        catch { }
                    }
                    catch { }
                }));

                InitSysButton();
            }
            finally
            {
                this.ResumeLayout(false);
            }
        }

        /// <summary>
        /// 启动异步初始化流程（按优先级分层执行）
        /// </summary>
        private void StartAsyncInitialization()
        {
            // 第一优先级：核心网络和处理线程 - 立即执行
            Task.Run(() =>
            {
                try
                {
                    InitNetWorkInfo();
                    NetWorkChangeEvent();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"高优先级初始化出错: {ex.Message}");
                }
            });

            Task.Run(() =>
            {
                try
                {
                    OcrProcessThread();
                    OcrResultProcessThread();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"高优先级初始化出错: {ex.Message}");
                }
            });

            // 第二优先级：依赖网络的功能 - 稍后执行
            Task.Run(() =>
            {
                try
                {
                    InitCommonTask();
                    CommonUpdate.InitUpdate();
                    InitThemeTask();

                    InitSearchEngines();
                    SetSearchEngine();
                }
                catch { }
            });

            // 第三优先级：UI增强功能 - 最后执行
            Task.Run(() =>
            {
                try
                {
                    InitOcrGroupItems();
                }
                catch { }
            });

            Task.Run(() =>
            {
                try
                {
                    InitOtherGuide();
                }
                catch { }
            });

            Task.Run(() =>
            {
                InitPlugButton();
            });

            Task.Run(() =>
            {
                try
                {
                    ImageHelper.InitImageValidateParam();
                }
                catch { }

                if (CommonSetting.启用本地识别)
                {
                    LocalOcrService.OpenOcrService((int)CommonSetting.本地识别端口,
                        (int)CommonSetting.本地识别线程数, false);
                }
            });

            // 首次运行引导
            if (Program.IsFirstRun)
            {
                CommonMethod.DetermineCallDelayed(this, () =>
                {
                    if (OwnedForms.Length <= 0)
                    {
                        ShowWindow();
                        ToolStripMenuItem_Click(tsmGuide, null);
                    }
                }, 10000);
            }
        }

        private void FrmMain_Shown(object sender, EventArgs e)
        {
            if (!CommonSetting.启动后打开主窗体)
                Hide();

            Opacity = 1;

            BeginInvoke(new Action(() =>
            {
                ChangeThemeDelegate?.Invoke();
                PerformDeferredInitialization();
            }));
        }

        /// <summary>
        /// 首屏渲染完成后延迟执行的重型初始化内容。
        /// </summary>
        private void PerformDeferredInitialization()
        {
            try
            {
                // 优化：分阶段异步初始化，避免一次性阻塞UI线程

                // 第1阶段：翻译和菜单初始化（异步执行）
                Task.Run(() =>
                {
                    try
                    {
                        BeginInvoke(new Action(() =>
                        {
                            // OCR 类型与纵排方向初始
                            InitItemTypeByValue(tsmContentType, OcrType.文本.ToString());

                            InitVerticalDirection();
                            if (string.IsNullOrEmpty(CommonSetting.竖排方向))
                            {
                                CommonSetting.竖排方向 = "上下左右";
                            }
                            InitItemTypeByValue(tsmVerticalDirection, CommonSetting.竖排方向);

                            InitTransItem();
                            InitSearchMenu();

                            // 语言菜单选中项
                            InitItemTypeByValue(tsmTransFrom, CommonSetting.源语言);
                            InitItemTypeByValue(tsmTransTo, CommonSetting.目标语言);
                        }));
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"翻译菜单异步初始化错误: {ex.Message}");
                    }
                });

                // 第2阶段：布局和显示初始化（延迟执行）
                CommonMethod.DetermineCallDelayed(this, () =>
                {
                    InitSpiltModel();
                    BindSpiltModel(NowSpiltModel);

                    InitItemTypeByValue(tsmPicViewModel, CommonSetting.展示模式);

                    UpdatePicViewModel(NowDisplayMode);
                }, 100);

                // 第3阶段：样式和权限处理（延迟执行）
                CommonMethod.DetermineCallDelayed(this, () =>
                {
                    ProcessForbidControls();
                }, 200);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DelayInit 错误: {ex.Message}");
            }
        }

        private UcContent _imageBox;

        /// <summary>
        /// 延迟初始化的图像预览控件（线程安全）
        /// </summary>
        private UcContent ImageBox
        {
            get
            {
                if (_imageBox == null)
                {
                    // 确保在UI线程中创建控件
                    if (InvokeRequired)
                    {
                        // 如果在非UI线程中调用，切换到UI线程
                        Invoke(new Action(() =>
                        {
                            CreateImageBoxInternal();
                        }));
                    }
                    else
                    {
                        // 已经在UI线程中，直接创建
                        CreateImageBoxInternal();
                    }
                }
                return _imageBox;
            }
        }

        /// <summary>
        /// 内部方法：创建ImageBox控件（必须在UI线程中调用）
        /// </summary>
        private void CreateImageBoxInternal()
        {
            if (_imageBox == null)
            {
                _imageBox = new UcContent(true)
                {
                    Dock = DockStyle.Fill,
                    NowDisplayMode = DisplayModel.图文模式,
                    ScalingStrategy = ImageScalingStrategy.FitInteractive
                };

                if (tbImageBox != null)
                {
                    tbImageBox.Controls.Add(_imageBox);
                    BeginInvoke(new Action(() =>
                    {
                        BindUcContent(_imageBox, EmptyContent, false, false);
                        _imageBox.RefreshStyle();
                        _imageBox.SetDragDrop();
                    }));
                }
            }
        }

        private void InitGuide()
        {
            var guide = new GuideEntity()
            {
                Items = new List<GuideItem>(),
                Title = "主界面功能区域介绍",
                BaseSize = new Size(454, 389),
                ShowSummary = true
            };
            guide.Items.Add(new GuideItem
            {
                Rect = new Rectangle(-350, 0, 280, 45),
                Desc = "个人中心：登录/注册、账号升级。\n功能菜单：日/夜间模式、置顶窗体、下拉菜单。",
                Title = "1、个人中心和菜单区域",
                Summary = true
            });
            guide.Items.Add(new GuideItem
            {
                Rect = new Rectangle(7, 33, 440, 140),
                Desc = "识别结果展示区域，由上方的标签和下方的内容区域组成。\n识别结果有多个时，可点击标签进行切换。",
                Title = "2、识别操作区域",
                Summary = true
            });
            guide.Items.Add(new GuideItem
            {
                Rect = new Rectangle(0, -50, 200, 50),
                Desc = "选择识别类型、预览模式、分段模式及语音播报。\n识别类型支持：文本（默认）、竖排、表格、公式、翻译。",
                Title = "3、识别配置区域",
                Summary = true
            });
            guide.Items.Add(new GuideItem
            {
                Rect = new Rectangle(344, 0, 34, 45),
                Desc = "截图、贴图、滚动/延时截图，各种玩法都在这里！\n截图/文件/粘贴/拖动/批量识别，全场景一个不落！\n白板、标尺、调色板、取色器，顺手才是好工具！\n快捷键等各种进阶玩法，开始探索吧。",
                Title = "4、打开系统设置，探索高级功能",
                Summary = true
            });
            CommonGuide.ShowGuide(this, guide);
        }

        private void SetDefaultTabContentName(TabPage tabPage)
        {
            tabPage.Text = "【识别内容】".CurrentText();
            tabPage.Tag = "识别内容";
        }

        public static OcrContent EmptyContent = new OcrContent { result = new ResultEntity { verticalText = "{}" } };

        private void InitContent()
        {
            tbImageBox = new TabPage()
            {
                Padding = Padding.Empty,
                Margin = Padding.Empty,
                Text = "图像预览".CurrentText(),
                AccessibleDescription = "图像预览"
            };
            tbContentText = new TabPage()
            {
                Padding = Padding.Empty,
                Margin = Padding.Empty,
                AccessibleDescription = "【识别内容】"
            };
            SetDefaultTabContentName(tbContentText);
            tbMain.Controls.Add(tbContentText);
            tbMain.Controls.Add(tbImageBox);
            //tbImageBox.Parent = null;
            tbMain.SelectedIndexChanged += (sender, e) =>
            {
                TbMainSelectedIndexChanged();
            };
        }

        private void TbMainSelectedIndexChanged()
        {
            if (tbMain.SelectedTab == tbImageBox)
            {
                ImageBox.ShowImageTool();
            }
            else
                (tbMain.SelectedTab?.Controls[0] as UcContent)?.ShowImageTool();
        }

        internal LoadingType NowLoadingType
        {
            get => nowLoadingType;
            set
            {
                nowLoadingType = value;
                ucLoading1.SetLoadingType(NowLoadingType);
            }
        }

        public static SearchEngine NowSearchEngine { get; set; }
        private static DisplayModel NowDisplayMode { get; set; }

        private void ProcessByImage(OcrType ocrType, ProcessBy processBy, Image image, bool isCapture,
            string tagUrl = null, bool isSearch = false)
        {
            if (CheckIsOnRec()) return;
            var fileName = CommonMethod.SaveImage(image, isCapture, false);
            bool? isSupportVertical = null;
            if (!Equals(processBy, ProcessBy.固定区域))
                image = ProcessImgSize(image);
            else
                isSupportVertical = true;

            OcrPoolProcess.ProcessByImage(ocrType, NowOcrGroupType, IsFromLeftToRight, IsFromTopToDown,
                CurrentTranslateFrom, CurrentTranslateTo, image, tagUrl, CommonString.StrDefaultImgType, fileName,
                isSearch, processBy, null, isSupportVertical);

            SetPicImage(image, tagUrl, Equals(processBy, ProcessBy.主界面));
        }

        private Image ProcessImgSize(Image image)
        {
            if (image != null && (image.Width < minWidth || image.Height < minHeight))
            {
                var newWidth = Math.Max(image.Width, minWidth);
                var newHeight = Math.Max(image.Height, minHeight);

                // 优化：直接创建最终的Bitmap，避免额外的内存复制
                var bitmap = new Bitmap(newWidth, newHeight);
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    graphics.Clear(Color.Transparent);
                    // 优化：降低渲染质量，提升处理速度
                    // 由于只是简单的图像居中放置（无缩放），不需要高质量插值
                    graphics.CompositingQuality = CompositingQuality.HighSpeed;
                    graphics.SmoothingMode = SmoothingMode.HighSpeed;
                    graphics.InterpolationMode = InterpolationMode.NearestNeighbor;

                    // 居中绘制原图像
                    graphics.DrawImage(image, (newWidth - image.Width) / 2,
                        (newHeight - image.Height) / 2,
                        image.Width, image.Height);
                }

                // 优化：直接返回处理后的bitmap，避免 new Bitmap(bitmap) 的额外复制
                return bitmap;
            }
            return image;
        }

        //private void ShowMsgControl()
        //{
        //    CommonMsg.ShowToWindow(this, new Point(8, (Height - tbMain.Top - 40) / 2), true);
        //}

        Button LoginInfo;

        Control sunCtrl, topCtrl;

        public override void OnThemeChange()
        {
            BeginInvoke(new Action(() =>
            {
                FrmTool.RefreshImage();
                RefreshUcContent();
                RefreshAllStyleImage();
            }));
        }

        private void TsmAutoTrans_CheckedChanged(object sender, EventArgs e)
        {
            IsCopyTrans = tsmAutoTrans.Checked;
            HookManager.MouseSelected -= mouseHook_MouseSelected;
            HookManager.MouseUp -= HookManager_MouseClick;
            HookManager.MouseDoubleClick -= mouseHook_DoubleClick;
            if (IsCopyTrans)
            {
                HookManager.MouseSelected += mouseHook_MouseSelected;
                HookManager.MouseUp += HookManager_MouseClick;
                HookManager.MouseDoubleClick += mouseHook_DoubleClick;
                HookManager.SubscribedToGlobalMouseEvents();
            }
            else
            {
                HookManager.UnsunscribeFromGlobalMouseEvents();
                HookManager_MouseClick(null, null);
            }

            CommonSetting.SetValue("划词翻译", IsCopyTrans);
        }

        private void Application_ApplicationExit(object sender, EventArgs e)
        {
            HookManager.UnsunscribeFromGlobalMouseEvents();
        }

        private void TxtContent_KeyDown(object sender, KeyEventArgs e)
        {
            if (!e.Control || e.KeyCode != Keys.V) return;
            try
            {
                var img = ClipboardService.GetImage();
                if (img != null)
                {
                    ProcessByImage(NowOcrType, ProcessBy.主界面, img, false);
                }
                else
                {
                    var imgFile = ClipboardService.GetOneFile();
                    if (!string.IsNullOrEmpty(imgFile))
                    {
                        ProcessFile(NowOcrType, ProcessBy.主界面, imgFile, null, null);
                    }
                    else if (sender != null)
                    {
                        var txt = ClipboardService.GetText(true);
                        if (!string.IsNullOrEmpty(txt))
                        {
                            if (sender is RichTextBox textBox)
                            {
                                textBox.AppendText(txt);
                            }
                            else if (sender is bool isTrans)
                            {
                                if (isTrans)
                                {
                                    ShowMiniSearch(txt, ClipboardContentType.文本, Point.Empty);
                                    //TransByText(txt);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("TxtContent_KeyDown", oe);
            }

            e.Handled = true;
        }

        private void InitPlugButton()
        {
            var lstPlug = CommonPlug.GetAllPlug();

            if (lstPlug == null || lstPlug.Count <= 0)
                return;

            CommonMethod.DetermineCall(this, () =>
            {
                foreach (var plug in lstPlug)
                {
                    //不满足会员等级条件，不展示
                    if (!CommonMethod.IsMatchUserLevel(plug.ShowLevel))
                    {
                        continue;
                    }
                    try
                    {
                        Control ctrl = null;
                        switch (plug.ButtonType)
                        {
                            case ButtonType.Image:
                                ctrl = AddWindowButton(WindowButtons.Plugin, plug.Click, plug.CanInverse);
                                break;
                            case ButtonType.ImageAndText:
                                ctrl = AddCustomButton(plug.Name, plug.GetImage(), plug.FontSize, plug.Click, plug.ForeColor);
                                if (ctrl != null)
                                {
                                    ChangeThemeDelegate += () =>
                                    {
                                        CommonMethod.DetermineCall(this, () =>
                                        {
                                            ((Button)ctrl).Image = plug.GetImage();
                                        });
                                    };
                                }
                                break;
                            case ButtonType.Text:
                                ctrl = AddCustomButton(plug.Name, plug.GetImage(), plug.FontSize, plug.Click, plug.ForeColor);
                                break;
                        }
                        if (ctrl != null)
                        {
                            ctrl.AccessibleDefaultActionDescription = plug.Image;
                            ctrl.Height += plug.Top * 2;
                            plug.Desc?.TransText((o, p) =>
                            {
                                tipMain.SetToolTip(ctrl, p);
                            });
                            InitForm.AddNewControl(ctrl);
                        }
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError("InitPlugButton", oe);
                    }
                }
                UpdateWindowButtonPosition();
            });
        }

        private void InitOtherGuide()
        {
            var lstPlug = CommonGuide.GetByForm("FrmMain");
            if (lstPlug == null || lstPlug.Count <= 0)
                return;

            BeginInvoke(new Action(() =>
            {
                foreach (var plug in lstPlug)
                {
                    try
                    {
                        var itemFrom = new ToolStripMenuItem
                        {
                            AccessibleDefaultActionDescription = plug.Code,
                            Text = plug.Title,
                            Tag = plug,
                            Font = cmsNotify.Items[0].Font,
                        };
                        if (string.IsNullOrEmpty(plug.Code))
                            plug.Code = tsmGuide.AccessibleDefaultActionDescription;
                        itemFrom.Image = itemFrom.SetResourceImage(plug.Code);
                        itemFrom.Click += (object sender, EventArgs e) =>
                        {
                            CommonGuide.ShowGuide(this, plug);
                        };
                        plug.Title.TransText((o, p) =>
                        {
                            itemFrom.Text = p;
                        });
                        tsmGuide.DropDownItems.Add(itemFrom);
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError("InitPlugButton", oe);
                    }
                }
            }));
        }

        private void InitSysButton()
        {
            var meunCtrl = AddWindowButton(WindowButtons.Menu, pnlSetting_Click);
            "系统菜单".TransText((o, p) =>
            {
                tipMain.SetToolTip(meunCtrl, p);
            });
            topCtrl = AddWindowButton(WindowButtons.Top, pnlTop_Click);
            tipMain.SetToolTip(topCtrl, TopMost ? "取消置顶".CurrentText() : "置顶窗体".CurrentText());
            sunCtrl = AddWindowButton(WindowButtons.Dark, pnlDark_Click);
            tipMain.SetToolTip(sunCtrl, CommonSetting.夜间模式 ? "日间模式".CurrentText() : "夜间模式".CurrentText());

            LoginInfo = AddCustomButton(StrToLoginStr.CurrentText(), Resources.qqKeFu, 17F, (sender, e) =>
            {
                CommonMethod.SetStyle(LoginInfo, ControlStyles.Selectable, false);
                if (!Program.IsLogined())
                {
                    BeginInvoke(new Action(() => LoginDelegate?.Invoke()));
                }
                else
                {
                    // 显示用户信息
                    using (var frmUser = new FrmUserInfo())
                    {
                        frmUser.Icon = Icon;
                        frmUser.ShowDialog(this);
                    }
                    //ShowMsgControl();
                }
            });
            LoginInfo.Name = "btnLogin";

            StaticValue.Handles.Add(Handle);
        }

        private void InitCommonTask()
        {
            var timerInfo = new TimerInfo
            {
                TimerType = "LoopRandomMinutes",
                DateValue = 5,
                IsExecFirst = false,
                TaskName = "FrmMainTask"
            };
            TimerTaskService.CreateTimerTaskService(timerInfo, CommonTaskRun).Start();
        }

        private bool CommonTaskRun(bool isUserUpdate, DateTime dtDate, string url)
        {
            CheckLoginStatus();
            try
            {
                UserMsgHelper.GetUserMsg();
            }
            catch { }
            try
            {
                // 清理所有图像资源
                ImageResourceManager.CleanupUnusedResources();
            }
            catch { }
            return true;
        }

        private void InitTransItem()
        {
            tsmAutoTrans = new ToolStripCheckBoxControl
            {
                Checked = false,
                ForeColor = Color.Black,
                Size = new Size(88, 29),
                Text = "划词翻译",
                ToolTipText = "鼠标拖选或者双击自动取词",
                Visible = false,
                AutoSize = true
            };
            stateStip.Items.Add(tsmAutoTrans);
            tsmAutoTrans.CheckedChanged += TsmAutoTrans_CheckedChanged;
            tsmAutoTrans.SetStyleManager(StyleManager);

            tsmTransFrom.DropDownItems.Clear();
            tsmTransTo.DropDownItems.Clear();
            foreach (TransLanguageTypeEnum v in Enum.GetValues(typeof(TransLanguageTypeEnum)))
            {
                var itemFrom = new ToolStripMenuItem
                {
                    AccessibleDescription = v.ToString(),
                    Text = v.ToString(),
                    Tag = v.GetHashCode(),
                    Font = cmsNotify.Items[0].Font,
                    Name = "cmsTransFromLanguageType" + v.GetHashCode()
                };
                itemFrom.Image = itemFrom.SetResourceImage(v.ToString());
                tsmTransFrom.DropDownItems.Add(itemFrom);
                if (!v.Equals(TransLanguageTypeEnum.自动))
                {
                    var itemTo = new ToolStripMenuItem
                    {
                        AccessibleDescription = v.ToString(),
                        Text = v.ToString(),
                        Tag = v.GetHashCode(),
                        Font = cmsNotify.Items[0].Font,
                        Name = "cmsTransToLanguageType" + v.GetHashCode()
                    };
                    itemTo.Image = itemTo.SetResourceImage(v.ToString());
                    tsmTransTo.DropDownItems.Add(itemTo);
                }
            }
        }

        private void InitThemeTask()
        {
            if (Equals(CommonSetting.日夜间模式, ThemeStyle.跟随系统.ToString()) || Equals(CommonSetting.日夜间模式, ThemeStyle.跟随日落.ToString()))
                CommonThemeManager.InitTheme(1);
            else
                CommonThemeManager.DisposeTask();
        }

        private void ProcessFile(OcrType ocrType, ProcessBy processBy, string fileName, string fileIdentity,
            string fileExt)
        {
            if (CheckIsOnRec()) return;
            if (!Program.IsLogined() || string.IsNullOrEmpty(fileName))
            {
                CommonMethod.ShowHelpMsg("未登录用户不支持文件识别，请先注册！".CurrentText());
                return;
            }

            fileExt = CommonMethod.GetFileExt(fileName, fileExt);

            if (!CommonString.IsValidateFileExt(fileExt))
            {
                MessageBox.Show(this, CommonString.GetNotSupportTypeMsg(), CommonString.StrReminder, MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (CommonString.IsValidateImageFileExt(fileExt) &&
                (Program.NowUser == null || Program.NowUser.IsSupportImageFile == false))
            {
                CommonMethod.ShowHelpMsg("当前账户不支持图片文件识别！".CurrentText());
                return;
            }

            if (ocrType.Equals(OcrType.翻译) && (Program.NowUser == null || Program.NowUser.IsSupportTranslate == false))
            {
                CommonMethod.ShowHelpMsg("当前账户不支持翻译功能！".CurrentText());
                return;
            }

            if (CommonString.IsValidateDocFileExt(fileExt) && !Equals(fileExt, CommonString.StrDefaultTxtType) &&
                (Program.NowUser == null || Program.NowUser.IsSupportDocFile == false))
            {
                CommonMethod.ShowHelpMsg("当前账户不支持文档文件识别！".CurrentText());
                return;
            }

            if (!Equals(processBy, ProcessBy.批量识别) && fileName.EndsWith(".pdf"))
            {
                using (var pdfProcess = new FormPdfProcess())
                {
                    pdfProcess.Icon = Icon;
                    pdfProcess.TopMost = true;
                    if (pdfProcess.ShowDialog(this) != DialogResult.OK)
                    {
                        return;
                    }

                    if (pdfProcess.IsProcessByImage)
                    {
                        ProcessByPDFFile(fileName, pdfProcess.IsSpiltImage);
                        return;
                    }
                }
            }

            var imgUrl = "";//VerticalDirection, 
            var bitmap = OcrPoolProcess.ProcessByFile(ocrType, NowOcrGroupType, IsFromLeftToRight, IsFromTopToDown,
                CurrentTranslateFrom, CurrentTranslateTo, fileName, imgUrl, fileExt, false,
                processBy, fileIdentity);

            if (!Equals(processBy, ProcessBy.批量识别))
                SetPicImage(bitmap, imgUrl, Equals(processBy, ProcessBy.主界面));
        }

        private void FrmMain_FormClosing(object sender, FormClosingEventArgs e)
        {
            WindowState = FormWindowState.Minimized;
            Visible = false;
            e.Cancel = true;
        }

        private void InitVerticalDirection()
        {
            var strTopLeft = GetVerticalDirectionStr(true, true);
            var tsmTopLeft = new ToolStripMenuItem
            {
                AccessibleDescription = strTopLeft,
                Tag = strTopLeft,
                Text = strTopLeft.CurrentText(),
                Font = this.Font,
                ToolTipText = "方向：从上到下，从左到右\n场景：通用印刷体\n\n例子：".CurrentText() + "\n白日依山尽，黄河入海流。\n欲穷千里目，更上一层楼！",
            };
            tsmTopLeft.Image = tsmTopLeft.SetResourceImage(strTopLeft);
            var strTopRight = GetVerticalDirectionStr(true, false);
            var tsmTopRight = new ToolStripMenuItem
            {
                AccessibleDescription = strTopRight,
                Tag = strTopRight,
                Text = strTopRight.CurrentText(),
                Font = this.Font,
                ToolTipText = "方向：从上到下，从右到左\n场景：古诗文\n\n例子：".CurrentText() + "\n更 欲 黄 白     登\n" +
                              "上 穷 河 日 唐 鹳\n" +
                              "一 千 入 依 王 雀\n" +
                              "层 里 海 山 之 楼\n" +
                              "楼 目 流 尽 涣",
            };
            tsmTopRight.Image = tsmTopRight.SetResourceImage(strTopRight);
            var strDownLeft = GetVerticalDirectionStr(false, true);
            var tsmDownLeft = new ToolStripMenuItem
            {
                AccessibleDescription = strDownLeft,
                Tag = strDownLeft,
                Text = strDownLeft.CurrentText(),
                Font = this.Font,
                ToolTipText = "方向：从下到上，从左到右\n场景：古籍，现代多见于车道上的文字\n\n例子：".CurrentText() + "\n道\n车\n交\n公",
            };
            tsmDownLeft.Image = tsmDownLeft.SetResourceImage(strDownLeft);
            var strDownRight = GetVerticalDirectionStr(false, false);
            var tsmDownRight = new ToolStripMenuItem
            {
                AccessibleDescription = strDownRight,
                Tag = strDownRight,
                Text = strDownRight.CurrentText(),
                Font = this.Font,
                ToolTipText = "方向：从下到上，从左到右\n场景：古籍，不常见".CurrentText(),
            };
            tsmDownRight.Image = tsmDownRight.SetResourceImage(strDownRight);
            tsmVerticalDirection.DropDownItems.AddRange(new ToolStripItem[] { tsmTopLeft, tsmTopRight, tsmDownLeft, tsmDownRight });
        }

        private void InitSearchEngines()
        {
            LstSearchEngine.Add(new SearchEngine("Baidu", "https://www.baidu.com/s?wd={0}"));
            LstSearchEngine.Add(new SearchEngine("Google", "https://www.google.com/search?q={0}"));
            LstSearchEngine.Add(new SearchEngine("Bing", "https://cn.bing.com/search?q={0}"));
            LstSearchEngine.Add(new SearchEngine("Yahoo", "https://search.yahoo.com/search?p={0}"));
            LstSearchEngine.Add(new SearchEngine("Sogou", "https://www.sogou.com/web?query={0}"));
            LstSearchEngine.Add(new SearchEngine("360", "https://www.so.com/s?q={0}"));
            LstSearchEngine.Add(new SearchEngine("DuckDuckGo", "https://duckduckgo.com/?q={0}"));
            LstSearchEngine.Add(new SearchEngine("Yandex", "https://www.yandex.com/search/?text={0}"));
            LstSearchEngine.Add(new SearchEngine("Naver", "https://search.naver.com/search.naver?query={0}"));
            LstSearchEngine.Add(new SearchEngine("AOL", "https://search.aol.com/aol/search?q={0}"));
            LstSearchEngine.Add(new SearchEngine("ASK", "https://www.ask.com/web?q={0}"));
        }

        private void SetSearchEngine()
        {
            NowSearchEngine = LstSearchEngine.FirstOrDefault(p => p.Name.Equals(CommonSetting.搜索引擎)) ?? LstSearchEngine.FirstOrDefault();
        }

        private void RefreshUcContent()
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.RefreshStyle(); });
        }

        private void RefreshToolStripMeunItem(ToolStripMenuItem item)
        {
            if (!string.IsNullOrEmpty(item.AccessibleDefaultActionDescription))
            {
                item.Image = ProcessStyleImage(ImageProcessHelper.GetResourceImage(item.AccessibleDefaultActionDescription));
            }
            try
            {
                foreach (var child in item.DropDownItems)
                {
                    if (child is ToolStripMenuItem childItem)
                    {
                        RefreshToolStripMeunItem(childItem);
                    }
                }
            }
            catch { }
        }

        private void RefreshAllStyleImage()
        {
            // 优化：异步处理图像刷新，避免阻塞UI线程
            Task.Run(() =>
            {
                try
                {
                    // 在后台线程预处理图像
                    var processedImages = new Dictionary<string, Image>();

                    // 预处理主要图像
                    var ocrTypeImage = ProcessStyleImage(ImageProcessHelper.GetResourceImage(NowOcrType.ToString()));
                    var displayModeImage = ProcessStyleImage(ImageProcessHelper.GetResourceImage(NowDisplayMode.ToString()));
                    var spiltModeImage = ProcessStyleImage(ImageProcessHelper.GetResourceImage(NowSpiltModel.ToString()));
                    var secondDirectionImage = ProcessStyleImage(ImageProcessHelper.GetResourceImage(GetVerticalDirectionStr(IsFromTopToDown, IsFromLeftToRight)));

                    // 切换到UI线程应用图像
                    BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            cmsNotify.StyleManager = CommonTheme.StyleManager;

                            // 应用预处理的图像
                            if (ocrTypeImage != null) tsmContentType.Image = ocrTypeImage;
                            if (displayModeImage != null) tsmPicViewModel.Image = displayModeImage;
                            if (spiltModeImage != null) tmsSpiltMode.Image = spiltModeImage;
                            if (secondDirectionImage != null) tsmVerticalDirection.Image = secondDirectionImage;

                            tmsSpiltMode.Text = NowSpiltModel.ToString().CurrentText();

                            // 异步处理菜单项图像
                            Task.Run(() => RefreshMenuItemsAsync());
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"UI图像应用错误: {ex.Message}");
                        }
                    }));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"图像预处理错误: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 异步刷新菜单项图像
        /// </summary>
        private void RefreshMenuItemsAsync()
        {
            try
            {
                var menuItems = new List<ToolStripMenuItem>();

                // 在UI线程中收集菜单项
                BeginInvoke(new Action(() =>
                {
                    foreach (var item in cmsNotify.Items)
                    {
                        if (item is ToolStripMenuItem cmItem)
                        {
                            menuItems.Add(cmItem);
                        }
                    }

                    // 处理收集到的菜单项
                    foreach (var menuItem in menuItems)
                    {
                        RefreshToolStripMeunItemAsync(menuItem);
                    }
                }));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"菜单项异步刷新错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 异步刷新单个菜单项图像
        /// </summary>
        private void RefreshToolStripMeunItemAsync(ToolStripMenuItem item)
        {
            if (item == null) return;

            Task.Run(() =>
            {
                Image processedImage = null;

                if (!string.IsNullOrEmpty(item.AccessibleDefaultActionDescription))
                {
                    processedImage = ProcessStyleImage(ImageProcessHelper.GetResourceImage(item.AccessibleDefaultActionDescription));
                }

                return processedImage;
            }).ContinueWith(task =>
            {
                var processedImage = task.Result;
                BeginInvoke(new Action(() =>
                {
                    try
                    {
                        if (processedImage != null && !item.IsDisposed)
                        {
                            item.Image = processedImage;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"菜单项UI更新错误: {ex.Message}");
                        processedImage?.Dispose();
                    }
                }));
            }, TaskScheduler.Default);

            // 递归处理子菜单项
            var childItems = new List<ToolStripMenuItem>();
            BeginInvoke(new Action(() =>
            {
                try
                {
                    foreach (var child in item.DropDownItems)
                    {
                        if (child is ToolStripMenuItem childItem)
                        {
                            childItems.Add(childItem);
                        }
                    }

                    // 处理子菜单项
                    foreach (var childItem in childItems)
                    {
                        RefreshToolStripMeunItemAsync(childItem);
                    }
                }
                catch { }
            }));
        }

        private string GetVerticalDirectionStr(bool isFromTopToDown, bool isFromLeftToRight)
        {
            var strTmp = string.Format("{0}{1}", isFromTopToDown ? "上下" : "下上", isFromLeftToRight ? "左右" : "右左");
            return strTmp;
        }

        private Image ProcessStyleImage(Image image)
        {
            if (image == null) return null;
            if (CommonSetting.夜间模式)
                image = ImageProcessHelper.InverseImage(new Bitmap(image));
            image = ImageProcessHelper.ScaleImage(image, CommonTheme.DpiScale);
            return image;
        }

        private void notifyMain_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            NotifyMainMouseDoubleClick(CommonSetting.双击托盘操作, ToolDoubleClickEnum.显示主窗体);
        }

        private void FrmTool_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            FrmTool.SetNotMove();
            NotifyMainMouseDoubleClick(CommonSetting.双击工具栏操作, ToolDoubleClickEnum.显示主窗体);
        }

        private void NotifyMainMouseDoubleClick(string enumStr, ToolDoubleClickEnum defaultClick)
        {
            var clickEnum = CommonSetting.ConvertToEnum(enumStr, defaultClick);
            switch (clickEnum)
            {
                case ToolDoubleClickEnum.显示主窗体:
                    ShowWindow();
                    break;
                case ToolDoubleClickEnum.截图识别:
                    截图识别ToolStripMenuItem_Click(null, null);
                    break;
                case ToolDoubleClickEnum.快速截图:
                    ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.快速截图), null);
                    break;
                case ToolDoubleClickEnum.截图编辑:
                    ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图编辑), null);
                    break;
                case ToolDoubleClickEnum.快速贴图:
                    ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.快速贴图), null);
                    break;
                case ToolDoubleClickEnum.截图贴图:
                    ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图贴图), null);
                    break;
                case ToolDoubleClickEnum.粘贴贴图:
                    文字贴图ToolStripMenuItem_Click(null, null);
                    break;
                case ToolDoubleClickEnum.显隐贴图:
                    ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.显隐贴图), null);
                    break;
            }
        }

        private void CmsNotify_Opening(object sender, CancelEventArgs e)
        {
            tsmShowTool.Visible = false;
            if (sender != null)
            {
                tsmGuide.Visible = true;
                tsmTrans.Visible = false;
                tsmSearch.Visible = false;
                tsmCopyTxt.Visible = false;
                tsmExportTxt.Visible = false;
                tsmTrace.Visible = false;
                tsmExportExcel.Visible = false;
                tsmSpiltExport.Visible = false;

                tsmToolImage.Visible = false;

                tsmShowMain.Visible = !Visible;

                var strShow = "显示";
                var strHide = "隐藏";
                tsmShowMain.Text = ((!Visible ? strShow : strHide) + "主窗体").CurrentText();
                tsmOcrGroupSpilt.Visible = true;

                var ctrl = (sender as ContextMenuStrip)?.SourceControl;
                if (ctrl is null || ctrl is Panel && ctrl.Name == "")
                {
                    tsmShowTool.Visible = true;
                    tsmShowTool.Text = ((!FrmTool.Visible ? strShow : strHide) + "工具栏").CurrentText();
                    //tsmToolImage.Visible = true;
                    //tsmToolImage.Text = ((!FrmTool.TopMost) ? "" : "取消") + "置顶工具栏";
                    //tsmTopWindow.Visible = true;
                    //tsmTopWindow.Text = ((!TopMost) ? "" : "取消") + "置顶窗体";
                }
                else if (ctrl is Form)
                {
                    tsmShowTool.Visible = true;
                    tsmShowTool.Text = ((!FrmTool.Visible ? strShow : strHide) + "工具栏").CurrentText();
                    tsmToolImage.Visible = true;
                }
                else
                {
                    tsmOcrGroupSpilt.Visible = !Visible;
                    tsmGuide.Visible = false;
                    if (ctrl is RichTextBox || ctrl is DualModeImageViewer)
                    {
                        tsmCopyTxt.Visible = true;
                        tsmExportTxt.Visible = true;
                        tsmTrace.Visible = true;
                        tsmSpiltExport.Visible = true;

                        if (ctrl is RichTextBox richTextBox)
                        {
                            if (richTextBox.SelectedText.Length > 0)
                            {
                                tsmSearch.Visible = true;
                                tsmTrans.Visible = Program.NowUser?.IsSupportTranslate == true;
                            }
                            var hasContent = !string.IsNullOrEmpty(richTextBox.Text);
                            tsmCopyTxt.Visible = hasContent;
                            tsmExportTxt.Visible = hasContent;
                            tsmTrace.Visible = hasContent;
                            tsmSpiltExport.Visible = hasContent;
                        }

                    }
                    else if (ctrl is DataGridViewEx dgv)
                    {
                        tsmExportExcel.Visible = true;
                        tsmSpiltExport.Visible = true;
                        tsmTrace.Visible = dgv.DataSource != null;
                    }
                }
            }
        }

        private bool InitItemTypeByValue(ToolStripDropDownButton toolStripDropDownButton, string objValue)
        {
            if (string.IsNullOrEmpty(objValue)) return false;
            var result = false;
            foreach (ToolStripDropDownItem item in toolStripDropDownButton.DropDownItems)
                if (item.Text.Equals(objValue) || item.Tag?.ToString().Equals(objValue) == true)
                {
                    result = true;
                    tsmDDL_DropDownItemClicked(toolStripDropDownButton, new ToolStripItemClickedEventArgs(item));
                    break;
                }

            return result;
        }

        private List<UcContent> GetNowBindUserControls()
        {
            var lstUcContent = new List<UcContent>();
            foreach (TabPage tab in tbMain.TabPages)
            {
                if (Equals(tab, tbImageBox))
                {
                    continue;
                }
                foreach (var ctrl in tab.Controls)
                    if (ctrl is UcContent ucContent)
                    {
                        lstUcContent.Add(ucContent);
                        break;
                    }
            }
            return lstUcContent;
        }

        private void SetUcContentSpiltMode(SpiltMode spiltMode)
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.SpiltModel = spiltMode; });
        }

        private void SetUcContentTranslateMode(bool showOldContent)
        {
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p => { p.IsShowOldContent = showOldContent; });
        }

        private void tsmDDL_DropDownItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {
            var item = e.ClickedItem;
            if (item == null || item.Tag == null) return;
            ToolStripDropDownButton ddlBtn = null;
            string strOpType;
            if (sender is string)
            {
                strOpType = sender.ToString();
            }
            else
            {
                ddlBtn = sender as ToolStripDropDownButton;
                strOpType = ddlBtn?.Tag?.ToString();
            }

            if (string.IsNullOrEmpty(strOpType) || ddlBtn == null) return;
            switch (strOpType)
            {
                case "识别方式":
                    ddlBtn.Text = item.Text.CurrentText();
                    ddlBtn.Image = ProcessStyleImage(item.Image);
                    {
                        var selectedType = (OcrType)BoxUtil.GetInt32FromObject(item.Tag?.ToString());
                        if (!selectedType.Equals(NowOcrType))
                        {
                            SetOcrType(selectedType);
                            IniHelper.SetValue("配置", strOpType, item.Text);
                        }
                    }
                    break;
                case "展示模式":
                    ddlBtn.Text = item.Text.CurrentText();
                    ddlBtn.Image = ProcessStyleImage(item.Image);
                    {
                        var selectedType = (DisplayModel)BoxUtil.GetInt32FromObject(item.Tag?.ToString());
                        if (!selectedType.Equals(NowDisplayMode))
                        {
                            NowDisplayMode = selectedType;
                            UpdatePicViewModel(NowDisplayMode);
                            CommonSetting.SetValue(strOpType, NowDisplayMode.ToString());
                        }
                    }
                    break;
                case "竖排方向":
                    ddlBtn.Text = item.Text.CurrentText();
                    ddlBtn.Image = ProcessStyleImage(ddlBtn.SetResourceImage(item.Tag?.ToString()));
                    IsFromTopToDown = item.Tag?.ToString().Contains("上下") == true;
                    IsFromLeftToRight = item.Tag?.ToString().Contains("左右") == true;
                    CommonSetting.SetValue(strOpType, GetVerticalDirectionStr(IsFromTopToDown, IsFromLeftToRight));
                    break;
                case "翻译语言From":
                    ddlBtn.Image = item.Image;
                    BindTranslateLanguage(item.Tag, true);
                    CommonSetting.SetValue("源语言", CurrentTranslateFrom.ToString());
                    break;
                case "翻译语言To":
                    ddlBtn.Image = item.Image;
                    BindTranslateLanguage(item.Tag, false);
                    CommonSetting.SetValue("目标语言", CurrentTranslateTo.ToString());
                    break;
            }
        }

        private void SetOcrType(OcrType selectedType)
        {
            if (!selectedType.Equals(NowOcrType))
            {
                NowOcrType = selectedType;
                CloseAllTabs();

                tsmPicViewModel.Visible = NowOcrType != OcrType.表格 && NowOcrType != OcrType.公式;
                tmsSpiltMode.Visible = NowOcrType != OcrType.翻译 && NowOcrType != OcrType.表格 && NowOcrType != OcrType.公式;
                tsmVerticalDirection.Visible = NowOcrType == OcrType.竖排;
                tsmTransFrom.Visible = NowOcrType == OcrType.翻译;
                tsmTransTo.Visible = NowOcrType == OcrType.翻译;
                tsmAutoTrans.Visible = NowOcrType == OcrType.翻译;
                tsmShowOldContent.Visible = NowOcrType == OcrType.翻译;
            }
        }

        private void BindSpiltModel(SpiltMode model)
        {
            NowSpiltModel = model;

            Task.Run(() =>
            {
                RefreshAllStyleImage();
            });

            tsmVerticalDirection.Visible = NowOcrType == OcrType.竖排;
            SetUcContentSpiltMode(model);
        }

        private void tsmShowOldContent_CheckedChanged(object sender, EventArgs e)
        {
            BindTranslateModel();
        }

        private void BindTranslateModel()
        {
            IsShowOldContent = tsmShowOldContent.Checked; // ? TranslateMode.显示译文 : TranslateMode.译文加原文;
            tsmShowOldContent.Checked = IsShowOldContent;
            //tsmShowOldContent.ToolTipText = IsShowOldContent ? "显示原文+译文".CurrentText() : "仅显示译文".CurrentText();
            SetUcContentTranslateMode(IsShowOldContent);
            CommonSetting.SetValue("显示原文", IsShowOldContent);
        }

        private void BindTranslateLanguage(object obj, bool isFrom)
        {
            var type = (TransLanguageTypeEnum)BoxUtil.GetInt32FromObject(obj?.ToString(), 0);
            if (isFrom)
            {
                CurrentTranslateFrom = type;
                tsmTransFrom.Text = CurrentTranslateFrom.ToString().CurrentText();
            }
            else
            {
                CurrentTranslateTo = type;
                tsmTransTo.Text = CurrentTranslateTo.ToString().CurrentText();
            }
        }

        private UcContent GetCurrentContent()
        {
            foreach (var ctrl in tbMain.SelectedTab.Controls)
                if (ctrl is UcContent ucContent)
                    return ucContent;
            return null;
        }

        private bool CheckIsOnRec()
        {
            if (CommonString.IsOnRec) CommonMethod.ShowHelpMsg(StrProcessOcr.CurrentText() + "," + CommonString.StrPleaseWait.CurrentText());
            return CommonString.IsOnRec;
        }

        private void 批量识别ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var frmBatch = new FrmBatchOCR { Icon = Icon };
            frmBatch.Show();
        }

        private void 批量压缩ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var frmBatch = new FrmBatchCompress { Icon = Icon };
            frmBatch.Show();
        }

        private void PDF转图片ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            using (var pdfFileDialog = new OpenFileDialog())
            {
                pdfFileDialog.Filter = "PDF|*.pdf;";
                pdfFileDialog.Title = "请选择文件".CurrentText();
                pdfFileDialog.RestoreDirectory = true;
                pdfFileDialog.Multiselect = false;
                if (pdfFileDialog.ShowDialog(this) == DialogResult.OK)
                {
                    ShowWindow();
                    ucLoading1.ShowLoading("处理中".CurrentText() + "," + CommonString.StrPleaseWait.CurrentText());
                    Task.Factory.StartNew(() =>
                    {
                        try
                        {
                            Dictionary<int, List<Bitmap>> dicImgContent = ConvertPdf2Image(pdfFileDialog.FileName, false);
                            string strFilePath = CommonString.DefaultRecPath + Path.GetFileName(pdfFileDialog.FileName).Replace(".", "_");
                            if (!Directory.Exists(strFilePath))
                            {
                                Directory.CreateDirectory(strFilePath);
                            }

                            foreach (var item in dicImgContent)
                            {
                                foreach (var bitmap in item.Value)
                                {
                                    bitmap.SafeSave(Path.Combine(strFilePath, item.Key.ToString() + ".png"), System.Drawing.Imaging.ImageFormat.Png);
                                }
                            }
                            CommonMethod.OpenFolder(strFilePath);
                            ucLoading1.ShowText("处理成功".CurrentText());
                        }
                        catch (Exception oe)
                        {
                            ucLoading1.ShowText("处理失败".CurrentText() + "，" + CommonString.StrRetry.CurrentText());
                            var dllName = CommonString.LoadDllByName(oe.Message, true);
                        }
                        finally
                        {
                            ucLoading1.CloseLoading(3);
                        }
                    });
                }
            }
        }

        private void 批量矫正ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var frmBatch = new FrmBatchDect { Icon = Icon };
            frmBatch.Show();
        }

        private void ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            cmsNotify.Hide();
            if (sender == null) return;
            var name = (sender as ToolStripItem)?.AccessibleDescription;
            var control = GetCurrentContent();
            switch (name)
            {
                case "快速上手":
                    if (tsmGuide.DropDownItems.Count > 0)
                    {
                        tsmGuide.DropDownItems[0].PerformClick();
                    }
                    else
                        InitGuide();
                    return;
                case "复制文本":
                    var strContent = control?.GetContentText();
                    if (!string.IsNullOrEmpty(strContent)) ClipboardService.SetText(strContent);
                    //txtContent.SelectAll();
                    //txtContent.Copy();
                    return;
                case "保存文本":
                    SaveTxtFile(control?.GetContentText());
                    return;
                case "导出Excel":
                    control?.ExportExcel();
                    return;
                case "文件识别":
                    if (CheckIsOnRec()) return;
                    using (var imgFileDialog = new OpenFileDialog())
                    {
                        imgFileDialog.Title = "请选择文件".CurrentText();
                        imgFileDialog.Filter = CommonString.GetAllFilter();
                        imgFileDialog.RestoreDirectory = true;
                        imgFileDialog.Multiselect = false;
                        if (imgFileDialog.ShowDialog(this) == DialogResult.OK)
                            ProcessFile(NowOcrType, ProcessBy.主界面, imgFileDialog.FileName, null, null);
                    }

                    return;
                case "PDF识别":
                    if (CheckIsOnRec()) return;
                    using (var pdfFileDialog = new OpenFileDialog())
                    {
                        pdfFileDialog.Filter = "PDF|*.pdf;";
                        pdfFileDialog.Title = "请选择文件".CurrentText();
                        pdfFileDialog.RestoreDirectory = true;
                        pdfFileDialog.Multiselect = false;
                        if (pdfFileDialog.ShowDialog(this) == DialogResult.OK)
                            ProcessFile(NowOcrType, ProcessBy.主界面, pdfFileDialog.FileName, null, null);
                    }

                    return;
                case "粘贴识别":
                    CopyAction();
                    return;
                case "识别链路":
                    control?.ShowTraceInfo();
                    return;
            }
        }

        private void CopyAction(bool isTrans = false)
        {
            if (CheckIsOnRec()) return;
            TxtContent_KeyDown(isTrans, new KeyEventArgs(Keys.Control | Keys.V));
        }

        // 滑动窗口裁剪图片并调用OCR API
        Dictionary<int, List<Bitmap>> ProcessImageWithSlidingWindow(byte[] lstByt, int windowHeight, int overlap)
        {
            var dicImage = new Dictionary<int, List<Bitmap>>();

            using (Image originalImage = Image.FromStream(new MemoryStream(lstByt)))
            {
                int width = originalImage.Width;
                int height = originalImage.Height;

                for (int y = 0; y < height; y += (windowHeight - overlap))
                {
                    // 防止裁剪超过图片底部
                    int remainingHeight = Math.Min(windowHeight, height - y);

                    // 裁剪区域
                    Rectangle cropRect = new Rectangle(0, y, width, remainingHeight);
                    Bitmap target = new Bitmap(cropRect.Width, cropRect.Height);

                    using (Graphics g = Graphics.FromImage(target))
                    {
                        g.CompositingMode = CompositingMode.SourceCopy;
                        g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                        g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                        g.DrawImage(originalImage, new Rectangle(0, 0, target.Width, target.Height),
                                    cropRect,
                                    GraphicsUnit.Pixel);
                    }
                    dicImage.Add(dicImage.Count + 1, new List<Bitmap>() { target });
                }
            }
            return dicImage;
        }

        private void ProcessByPDFFile(string fileName, bool isSpiltImage)
        {
            Dictionary<int, List<Bitmap>> dicImgContent = ConvertPdf2Image(fileName, isSpiltImage);
            ProcessByImage(dicImgContent, fileName);
        }

        private void ProcessByLongImage(byte[] lstByt, string fileName)
        {
            Dictionary<int, List<Bitmap>> dicImgContent = ProcessImageWithSlidingWindow(lstByt, 2000, 10);
            ProcessByImage(dicImgContent, fileName);
        }

        private void ProcessByImage(Dictionary<int, List<Bitmap>> dicImgContent, string fileName)
        {
            CloseAllTabs();
            ShowWindow();
            ucLoading1.ShowLoading("处理中".CurrentText() + "," + CommonString.StrPleaseWait.CurrentText());
            Task.Factory.StartNew(() =>
            {
                try
                {
                    //CommonMethod.ShowLoading();
                    var objLock = "";
                    Dictionary<int, string> dicContent = new Dictionary<int, string>();
                    var execPerTime = Program.GetExecSleepTime();
                    foreach (var pageIndex in dicImgContent.Keys)
                    {
                        var pageImages = dicImgContent[pageIndex];

                        for (int ii = 0; ii < pageImages.Count; ii++)
                        {
                            var img = pageImages[ii];
                            var byts = ImageProcessHelper.ImageToByte(img);
                            string imgUrl = null;// isUplpadImg ? UploadByFile(byts, CommonString.StrDefaultImgType, false) : null;

                            OcrContent ocr = null;

                            var stop = Stopwatch.StartNew();
                            var processEntity = OcrPoolProcess.GetProcessEntityByBytes(OcrType.文本, NowOcrGroupType
                                , IsFromLeftToRight, IsFromTopToDown
                                , CurrentTranslateFrom, CurrentTranslateTo, byts,
                                imgUrl, CommonString.StrDefaultImgType, false, ProcessBy.主界面, null, null);
                            processEntity.AddToPool = false;
                            var strOnePage = pageImages.Count > 1 ? "中第{0}/{1}张图片".CurrentText() : string.Empty;
                            for (int i = 0; i < 10; i++)
                            {
                                if (ocr == null || ocr.result?.HasResult == false)
                                {
                                    if (i == 0)
                                    {
                                        ucLoading1.ShowText(string.Format("正在识别第{0}/{1}页{2}…".CurrentText(), pageIndex,
                                            dicImgContent.Count, string.Format(strOnePage, ii + 1, pageImages.Count)));
                                    }

                                    ocr = GetOcrContentByBytes(processEntity);
                                    if (stop.ElapsedMilliseconds < execPerTime)
                                        Thread.Sleep((int)(execPerTime - stop.ElapsedMilliseconds));
                                }
                                else
                                {
                                    break;
                                }
                            }

                            lock (objLock)
                            {
                                var resultSpiltText =
                                //string.Format("\n第{1}/{2}页\n", ocr?.processName, imgKey.Key, dicImgContent.Count) +
                                    ocr?.result?.GetTextResult(CommonSetting.首行缩进, false, IsShowOldContent, NowSpiltModel, Equals(ocr.ocrType, OcrType.翻译), ocr.processName);
                                // 处理节点：【{0}】
                                if (dicContent.ContainsKey(pageIndex))
                                {
                                    dicContent[pageIndex] += resultSpiltText;
                                }
                                else
                                {
                                    dicContent.Add(pageIndex, resultSpiltText);
                                }

                                ucLoading1.ShowText(string.Format("第{0}/{1}页{2} {3}！".CurrentText(), pageIndex,
                                    dicImgContent.Count, string.Format(strOnePage, ii + 1, pageImages.Count), "处理成功".CurrentText()));
                            }

                            byts = null;
                            img.Dispose();
                            img = null;
                            ocr = null;
                        }
                    }

                    ucLoading1.ShowText("处理中".CurrentText() + "，" + CommonString.StrPleaseWait.CurrentText());
                    var sb = new StringBuilder();
                    for (int i = 1; i <= dicContent.Count; i++)
                    {
                        sb.AppendLine(dicContent[i]?.Replace("\n", Environment.NewLine));
                    }

                    string strFileName = CommonString.DefaultRecPath + Path.GetFileNameWithoutExtension(fileName) +
                                         "-" + NowOcrGroupType.ToString() + ".txt";
                    try
                    {
                        if (!Directory.Exists(CommonString.DefaultRecPath))
                        {
                            Directory.CreateDirectory(CommonString.DefaultRecPath);
                        }

                        if (File.Exists(strFileName))
                        {
                            File.Delete(strFileName);
                        }

                        File.WriteAllText(strFileName, sb.ToString());
                        ucLoading1.ShowText("处理成功".CurrentText() + "\n" + Path.GetFileName(fileName));
                        CommonMethod.OpenFolderWithFile(strFileName);
                    }
                    catch
                    {
                        ucLoading1.ShowText("处理成功".CurrentText());
                        ShowContentText(sb.ToString());
                    }

                    sb.Clear();
                }
                catch (Exception oe)
                {
                    ucLoading1.ShowText("处理中".CurrentText() + "，" + CommonString.StrRetry.CurrentText());
                    var dllName = CommonString.LoadDllByName(oe.Message, true);
                }
                finally
                {
                    ucLoading1.CloseLoading(3);
                }
            });
        }

        /// <summary>
        /// 将PDF文档转换为图片的方法
        /// </summary>
        /// <param name="pdfInputPath">PDF文件路径</param>
        /// <param name="isSpiltImage"></param>
        private Dictionary<int, List<Bitmap>> ConvertPdf2Image(string pdfInputPath, bool isSpiltImage)
        {
            Dictionary<int, List<Bitmap>> dicImgContent = new Dictionary<int, List<Bitmap>>();
            int definition = 3;
            var cutHeight = 38;
            //const string imgPath = @"C:\Users\<USER>\Desktop\pdf\";
            using (var pdfFile = PDFFile.Open(pdfInputPath))
            {
                int startPageNum = 1, endPageNum = pdfFile.PageCount;

                for (int i = startPageNum; i <= endPageNum; i++)
                {
                    Bitmap pageImage = pdfFile.GetPageImage(i - 1, 56 * definition);
                    var lstImage = new List<Bitmap>();
                    if (isSpiltImage)
                    {
                        Bitmap leftPageImage = new Bitmap(pageImage.Width / 2, pageImage.Height);
                        Bitmap rightPageImage = new Bitmap(pageImage.Width / 2, pageImage.Height);
                        using (Graphics g = Graphics.FromImage(leftPageImage))
                        {
                            g.InterpolationMode = InterpolationMode.High;
                            //重新画图的时候Y轴减去130，高度也减去130  这样水印就看不到了
                            g.DrawImage(pageImage, new Rectangle(0, 0, leftPageImage.Width, leftPageImage.Height),
                                new Rectangle(0, cutHeight, pageImage.Width / 2, pageImage.Height - cutHeight), GraphicsUnit.Pixel);
                        }

                        using (Graphics g = Graphics.FromImage(rightPageImage))
                        {
                            g.InterpolationMode = InterpolationMode.High;
                            //重新画图的时候Y轴减去130，高度也减去130  这样水印就看不到了
                            g.DrawImage(pageImage, new Rectangle(0, 0, rightPageImage.Width, rightPageImage.Height),
                                new Rectangle(pageImage.Width / 2, cutHeight, pageImage.Width / 2, pageImage.Height - cutHeight),
                                GraphicsUnit.Pixel);
                        }

                        ////保存图片
                        //leftPageImage.Save(imgPath + i + "_1.jpg");
                        //rightPageImage.Save(imgPath + i + "_2.jpg");
                        lstImage.Add(leftPageImage);
                        lstImage.Add(rightPageImage);
                    }
                    else
                    {
                        Bitmap newPageImage = new Bitmap(pageImage.Width, pageImage.Height);
                        using (Graphics g = Graphics.FromImage(newPageImage))
                        {
                            g.InterpolationMode = InterpolationMode.High;
                            //重新画图的时候Y轴减去130，高度也减去130  这样水印就看不到了
                            g.DrawImage(pageImage, new Rectangle(0, 0, newPageImage.Width, newPageImage.Height),
                                new Rectangle(0, cutHeight, pageImage.Width, pageImage.Height - cutHeight), GraphicsUnit.Pixel);

                            //保存图片
                            //newPageImage.Save(imgPath + i + ".jpg");
                        }

                        lstImage.Add(newPageImage);
                    }

                    dicImgContent.Add(i, lstImage);
                }

                //pdfFile.Dispose();
            }

            return dicImgContent;
        }

        //private string UploadByFile(byte[] byts, string ext, bool isShowLoading = true)
        //{
        //    var imgUrl = string.Empty;
        //    try
        //    {
        //        if (isShowLoading)
        //            ucLoading1.ShowLoading(StrUploadImg);

        //        imgUrl = OcrHelper.UploadImage(byts);
        //    }
        //    catch
        //    {
        //    }

        //    return imgUrl;
        //}

        private void SaveTxtFile(string txt)
        {
            using (var saveFileDialog1 = new SaveFileDialog())
            {
                saveFileDialog1.Filter = "TXT|*.txt;*.doc;*.docx;";
                saveFileDialog1.Title = "选择保存位置".CurrentText();
                saveFileDialog1.FileName = "文本".CurrentText() + "_" + ServerTime.DateTime.ToDateStr("yyyyMMddhhmmss") + ".txt";
                saveFileDialog1.FilterIndex = 0;
                if (saveFileDialog1.ShowDialog(this) == DialogResult.OK && saveFileDialog1.FileName != "")
                {
                    CommonResult.ExportToTxt(txt, saveFileDialog1.FileName);
                }
            }
        }

        private void 识别历史ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var frmBatch = new FormRecent { Icon = Icon };
            frmBatch.Show(this);
        }

        private void 截图识别ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (CheckIsOnRec()) return;
            var img = CaptureImageByType(false);
            if (img?.Image != null)
            {
                var ocrType = IsOcrForSearch ? OcrType.文本 : NowOcrType;
                ProcessByImage(ocrType, ProcessBy.主界面, img.Image, true, null, IsOcrForSearch);
            }

            IsOcrForSearch = false;
        }

        private void 白板ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Bitmap canvas = null;
            if (CommonSetting.使用纯色背景)
            {
                Rectangle rect = Rectangle.Empty;
                _ = Screenshot.CaptureFullscreen(ref rect, true);
                canvas = new Bitmap(rect.Width, rect.Height);
                using (Graphics graphics = Graphics.FromImage(canvas))
                {
                    graphics.Clear(CommonSetting.默认白板背景色);
                }
            }
            var image = CommonMethod.EditImage(new CommonMethod.CaptureImageContent() { Image = canvas }, RegionCaptureMode.WhiteBoard);
        }

        private void 放大镜ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            using (var form = new RegionCaptureForm(RegionCaptureMode.Magnifier, CommonMethod.GetRegionCaptureOptions(false, "放大镜")))
            {
                try
                {
                    form.Icon = CommonMethod.GetApplicationIcon();
                    form.ShowDialog();
                    form.IsExitCapture = true;
                }
                catch { }
            }
        }

        private void 取色器ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var color = Color.Transparent;
            using (var form = new RegionCaptureForm(RegionCaptureMode.ScreenColorPicker, CommonMethod.GetRegionCaptureOptions(false, "取色器")))
            {
                try
                {
                    form.Icon = CommonMethod.GetApplicationIcon();
                    form.ShowDialog();
                    form.IsExitCapture = true;
                    if (form.Result != RegionResult.Close)
                    {
                        if (form.ShapeManager != null)
                        {
                            try
                            {
                                color = form.ShapeManager.GetCurrentColor();
                            }
                            catch { }
                        }
                    }
                }
                catch { }
            }
            if (!Equals(Color.Transparent, color))
            {
                CommonMethod.ShowColorInfo(color, "取色器".CurrentText());
            }
        }

        private void 调色板ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ColorPickerForm.PickColor(Color.Black, out var newColor, false, this);
        }

        private void 标尺工具ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var scroll = new RulerForm { Icon = Icon };
            scroll.Show(this);
        }

        private void 贴图ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var result = CaptureImageByType(true, CaptureActions.截图贴图.ToString());
            this.ViewImageWithLocation(result.Image,
                new Point(result.Rectangle.X, result.Rectangle.Y));
        }

        private void 截图ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var img = CaptureImageByType(true, CaptureActions.截图编辑.ToString());
            if (img?.Image != null)
            {
                CommonMethod.SaveImage(img.Image, true, true);
            }
        }

        private void 文字贴图ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var data = new ClipboardTextEntity
            {
                Type = ClipboardDataType.Image,
                Image = ClipboardService.GetImage()
            };
            if (data.Image == null) data = ClipboardService.GetRtfHtmlImage();
            this.ViewTextImage(data);
        }

        private void InitScreenShot()
        {
            CommonEnumAction<CaptureActions>.AddEnumItemsContextMenu(cmsNotify, ProcessCaptureClickAction);
        }

        private bool HideFormTool(bool isHideMain = false)
        {
            var isShowTool = FrmTool.Visible;
            if (isShowTool)
                FrmTool.Visible = false;
            if (isHideMain)
            {
                Hide();
            }

            frmOCR?.HideWindow(true);
            return isShowTool;
        }

        private void ProcessCaptureClickAction(object sender, EventArgs e)
        {
            var clickItem = sender as ToolStripItem;
            var info = (EnumInfo)clickItem?.Tag;
            if (info != null)
            {
                var action = (CaptureActions)info.Value;
                Image image = null;
                Rectangle rectangle = Rectangle.Empty;
                var isShowTool = HideFormTool();
                switch (action)
                {
                    case CaptureActions.快速截图:
                        image = CaptureImageByType(false, action.ToString())?.Image;
                        break;
                    case CaptureActions.截图编辑:
                        image = CaptureImageByType(true, action.ToString())?.Image;
                        break;
                    case CaptureActions.上次区域截图:
                        if (StaticValue.LastRectangle.IsEmpty)
                        {
                            image = CaptureImageByType(false, action.ToString())?.Image;
                        }
                        else
                        {
                            var fullScreenImage = Screenshot.CaptureFullscreen(ref rectangle, true);
                            image = ImageProcessHelper.CropBitmap(fullScreenImage, StaticValue.LastRectangle);
                        }
                        break;
                    case CaptureActions.延时截图:
                        var delayMilSec = (int)(CommonSetting.截图延时 * 1000);
                        if (delayMilSec > 0)
                        {
                            DelayCapture(delayMilSec);
                            image = CaptureImageByType(true, action.ToString())?.Image;
                        }
                        break;

                    case CaptureActions.快速贴图:
                        var fastPasteResult = CaptureImageByType(false, action.ToString());
                        image = fastPasteResult?.Image;
                        if (fastPasteResult != null)
                            this.ViewImageWithLocation(image,
                                new Point(fastPasteResult.Rectangle.X, fastPasteResult.Rectangle.Y));
                        break;
                    case CaptureActions.粘贴贴图:
                        文字贴图ToolStripMenuItem_Click(null, null);
                        break;
                    case CaptureActions.截图贴图:
                        var tieTuResult = CaptureImageByType(true, action.ToString());
                        image = tieTuResult?.Image;
                        if (image != null)
                            this.ViewImageWithLocation(image,
                                new Point(tieTuResult.Rectangle.X, tieTuResult.Rectangle.Y));
                        break;
                    case CaptureActions.显隐贴图:
                        try
                        {
                            var isHasPasteWindow = false;
                            var froms = Application.OpenForms.OfType<FrmPasteImage>().Where(x => !x.IsDisposed);
                            var frmPasteImages = froms as FrmPasteImage[] ?? froms.ToArray();
                            var isShow = frmPasteImages.Any(x => !x.Visible);
                            foreach (var form in frmPasteImages)
                            {
                                isHasPasteWindow = true;
                                form.Visible = isShow;
                            }

                            if (!isHasPasteWindow)
                            {
                                CommonMethod.ShowHelpMsg("当前无活动中的贴图窗口！".CurrentText());
                            }
                        }
                        catch (Exception oe)
                        {
                            Console.WriteLine(oe.Message);
                        }
                        break;
                    case CaptureActions.活动窗口:
                        image = Screenshot.CaptureActiveWindow(ref rectangle);
                        break;
                    case CaptureActions.全屏截图:
                        image = Screenshot.CaptureFullscreen(ref rectangle, true);
                        break;
                    case CaptureActions.活动显示器:
                        image = Screenshot.CaptureActiveMonitor(ref rectangle);
                        break;
                    case CaptureActions.固定区域截图:
                        //var item = CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.固定区域);
                        FixAreaItemClick(clickItem);
                        break;
                    case CaptureActions.滚动截屏:
                        var obj = CommonMethod.CatchImage("滚动截图");
                        var result = obj != null && obj.Window != null;
                        if (result)
                        {
                            using (var manager = new ScrollingCapture.ScrollingCaptureManager(
                                       new ScrollingCaptureOptions()
                                       {
                                           ScrollDelay = (int)CommonSetting.ScrollDelay,
                                           StartDelay = (int)CommonSetting.StartDelay,
                                           ScrollAmount = (int)CommonSetting.ScrollAmount
                                       }, obj.Window, obj.Rectangle))
                            {
                                manager.StartCapture();
                                this.ViewImage(ImageResourceManager.GetManagedImage(manager.Result));
                            }
                        }
                        break;
                    case CaptureActions.打开截图文件夹:
                        CommonMethod.OpenFolder(CommonString.DefaultImagePath);
                        break;
                    case CaptureActions.截屏快捷键配置:
                    case CaptureActions.贴图快捷键配置:
                    case CaptureActions.截屏配置:
                    case CaptureActions.贴图配置:
                    case CaptureActions.滚动截屏配置:
                        系统设置SToolStripMenuItem_Click(sender, e);
                        break;
                }

                if (isShowTool) FrmTool.Visible = true;
                if (image != null && action.ToString().Contains("贴图".CurrentText()) == false)
                {
                    CommonMethod.SaveImage(image, true, true);
                }
            }
        }

        private void DelayCapture(int delayMilSec)
        {
            var size = new Size(750, 500);
            var rect = new Rectangle((Screen.PrimaryScreen.WorkingArea.Width - size.Width) / 2, (Screen.PrimaryScreen.WorkingArea.Height - size.Height) / 2, size.Width, size.Height);
            var shadowForm = InitShadowForm(null, false, false, true, new Size(rect.Size.Width + 4, rect.Size.Height + 4),
                new Point(rect.X - 2, rect.Y - 2));
            shadowForm.Text = "延时截图".CurrentText();
            shadowForm.TopMost = true;
            shadowForm.Show();
            var entity = new FixAreaCaptureEntity()
            {
                BaseRect = rect,
                DelayMilSec = delayMilSec,
                IntervalMilSecond = 3500,
                Item = null,
                LoopTimes = 0,
                ShadowForm = shadowForm
            };
            int delayMinSec = 1000;
            var font = CommonString.GetSysBoldFont(260F);
            while (!CommonString.IsExit && entity.IsRun && entity.DelayMilSec > 0)
            {
                SetShadowFormText(entity.ShadowForm, Math.Ceiling(entity.DelayMilSec * 1.0 / 1000).ToString("F0"), font);
                Thread.Sleep(delayMinSec);
                entity.DelayMilSec -= delayMinSec;
            }
            entity.ShadowForm?.Close();
            entity.ShadowForm?.Dispose();
            entity.ShadowForm = null;
        }

        private void FixAreaItemClick(ToolStripItem item)
        {
            if (item == null)
            {
                return;
            }
            var strStop = "停止".CurrentText();
            var text = item.AccessibleDescription.CurrentText();
            if (item.Text.StartsWith(strStop))
            {
                item.Text = text;
                this.GetShadowForm(text)?.Close();
            }
            else
            {
                var tmp = CaptureImageByType(false, item.AccessibleDescription);
                var rectangle = tmp?.Rectangle;
                if (rectangle == null || rectangle.Value.IsEmpty)
                {
                    return;
                }

                item.Text = strStop + text;

                FixAreaCaptureMethod(item, tmp);
            }
        }

        private void FixAreaCaptureMethod(ToolStripItem item, CommonMethod.CaptureImageContent content)
        {
            bool isOcr = item.AccessibleDescription != null && (item.AccessibleDescription.Contains("识别") || item.AccessibleDescription.Contains("翻译"));

            var rect = content.Rectangle;
            var shadowForm = InitShadowForm(item, true, isOcr, true, new Size(rect.Size.Width + 4, rect.Size.Height + 4),
                new Point(rect.X - 2, rect.Y - 2));

            int delayMilSec = 0;
            int intervalMilSec = 3500;
            int loopTimes = isOcr ? 100 : 1;
            bool isShowInMainWindow = false;

            SetShadowFormText(shadowForm, "等待操作…".CurrentText());
            var frmArea = new FormAreaCapture() { Icon = Icon };
            frmArea.CaptureLocation = rect.Location;
            frmArea.CaptureSize = new Size(shadowForm.Size.Width, shadowForm.Size.Height - (shadowForm.IsShowTitle ? 35 : 0));
            frmArea.IntervalMilSecond = intervalMilSec;
            frmArea.DelayMilSecond = delayMilSec;
            frmArea.LoopTimes = loopTimes;
            frmArea.TopMost = true;
            frmArea.Text = item.AccessibleDescription;
            shadowForm.TopMost = false;
            if (frmArea.ShowDialog(this) != DialogResult.OK)
            {
                shadowForm.Close();
                return;
            }

            if (!frmArea.CaptureLocation.IsEmpty)
            {
                rect.Location = frmArea.CaptureLocation;
            }

            if (!frmArea.CaptureSize.IsEmpty)
            {
                rect.Size = frmArea.CaptureSize;
            }

            delayMilSec = frmArea.DelayMilSecond;
            loopTimes = frmArea.LoopTimes;
            intervalMilSec = Math.Max(frmArea.IntervalMilSecond, 1000);
            isShowInMainWindow = frmArea.IsShowInMainWindow;

            shadowForm.Location = new Point(rect.X - 2, rect.Y - 2);
            shadowForm.Size = new Size(rect.Size.Width + 4, rect.Size.Height + 4);

            shadowForm.ForceActivate();
            shadowForm.TopMost = true;
            shadowForm.SetShadowTopMost(true);

            if (rect.Width < MinFixAreaWidth) rect.Width = MinFixAreaWidth;
            if (rect.Height < MinFixAreaWidth) rect.Height = MinFixAreaWidth;

            var execPerTime = Program.GetExecSleepTime();
            var entity = new FixAreaCaptureEntity()
            {
                BaseRect = rect,
                DelayMilSec = delayMilSec,
                IntervalMilSecond = Math.Max(execPerTime, intervalMilSec),
                Item = item,
                LoopTimes = loopTimes,
                ShadowForm = shadowForm,
                IsShowInMainWindow = isShowInMainWindow,
                Content = content
            };
            FixedFieldCapture(entity);
        }

        Rectangle CalculateCurrentScreenshotRectangle(Rectangle previousWindowRect, Rectangle previousScreenshotRect, Rectangle currentWindowRect)
        {
            // Step 1: Calculate the relative position of the previous screenshot rectangle
            float relativeX = previousScreenshotRect.X - previousWindowRect.X;
            float relativeY = previousScreenshotRect.Y - previousWindowRect.Y;

            // Step 2: Calculate the width and height scale factors
            float widthScale = (float)currentWindowRect.Width / previousWindowRect.Width;
            float heightScale = (float)currentWindowRect.Height / previousWindowRect.Height;

            // Step 3: Calculate new width and height for the current screenshot rectangle
            int newWidth = (int)(previousScreenshotRect.Width * widthScale);
            int newHeight = (int)(previousScreenshotRect.Height * heightScale);

            // Step 4: Calculate the new top-left corner for the current screenshot rectangle
            int newX = currentWindowRect.X + (int)(relativeX * widthScale);
            int newY = currentWindowRect.Y + (int)(relativeY * heightScale);

            // Create the new Rectangle for the current screenshot
            Rectangle currentScreenshotRect = new Rectangle(newX, newY, newWidth, newHeight);

            // Step 5: Ensure the rectangle does not go out of bounds of the current window
            if (currentScreenshotRect.Right > currentWindowRect.Right)
            {
                currentScreenshotRect.Width = currentWindowRect.Right - currentScreenshotRect.X;
            }

            if (currentScreenshotRect.Bottom > currentWindowRect.Bottom)
            {
                currentScreenshotRect.Height = currentWindowRect.Bottom - currentScreenshotRect.Y;
            }

            return currentScreenshotRect;
        }
        private void FixedFieldCapture(FixAreaCaptureEntity entity)
        {
            Task.Factory.StartNew(() =>
            {
                int delayMinSec = 1000;
                while (!CommonString.IsExit && entity.IsRun && entity.DelayMilSec > 0)
                {
                    //SetShadowFormText(entity.ShadowForm,
                    //    string.Format("{0}秒后开始", Math.Ceiling(entity.DelayMilSec * 1.0 / 1000)));
                    SetShadowFormTextTick(entity.ShadowForm, (int)Math.Ceiling(entity.DelayMilSec * 1.0 / 1000));
                    Thread.Sleep(delayMinSec);
                    entity.DelayMilSec -= delayMinSec;
                }

                SetShadowFormText(entity.ShadowForm);
                var nowLoopTime = 0;
                var isOcr = entity.IsOcr;
                isFixShowResultInMainWindow = entity.IsShowInMainWindow;
                if (isOcr)
                {
                    SetShadowFormContent(entity.ShadowForm);
                }
                Bitmap lastImage = null;
                long lastOcrTick = -1;
                bool isHandleCapture = entity.Content.Window != null;
                var baseRect = entity.BaseRect;
                if (isHandleCapture)
                {
                    baseRect = entity.BaseRect;// entity.Content.Window.Rectangle;
                    Rectangle rectNew = entity.BaseRect;
                    entity.Content.Window.Handle = Screenshot.GetParentHandle(entity.Content.Window.Handle, ref rectNew);
                    entity.Content.Window.Rectangle = rectNew;
                }
                while (!CommonString.IsExit && entity.IsRun && nowLoopTime < entity.LoopTimes)
                    if (!CommonString.IsOnRec)
                    {
                        //Console.WriteLine(string.Format("{0} 开始循环", DateTime.Now.ToString("HH:mm:ss fff")));
                        nowLoopTime++;
                        Bitmap image = null;
                        if (isHandleCapture)
                        {
                            var rectClient = new Rectangle();
                            var windowImage = Screenshot.CaptureHandle(entity.Content.Window.Handle, ref rectClient);
                            if (windowImage == null)
                            {
                                isHandleCapture = false;
                                nowLoopTime--;
                                continue;
                            }
                            if (nowLoopTime == 1)
                            {
                                if (ImageProcessHelper.CheckImage(windowImage))
                                {
                                    isHandleCapture = false;
                                    nowLoopTime--;
                                    continue;
                                }
                            }
                            if (windowImage != null && (rectClient.X >= 0 || rectClient.Y >= 0))
                            {
                                //windowImage.Save(nowLoopTime + "full.png");
                                var rectNew = CalculateCurrentScreenshotRectangle(entity.Content.Window.Rectangle, baseRect, rectClient);
                                //entity.ShadowForm.Location = new Point(rectNew.X, rectNew.Y);
                                //entity.ShadowForm.Size = rectNew.Size;
                                rectNew = new Rectangle(rectNew.X - rectClient.X, rectNew.Y - rectClient.Y, rectNew.Width, rectNew.Height);
                                image = ImageProcessHelper.CropBitmap(windowImage, rectNew);
                            }
                        }
                        else
                        {
                            var location = entity.ShadowForm?.Location ?? entity.BaseRect.Location;
                            var rect = new Rectangle(location, entity.ShadowForm?.Size ?? entity.BaseRect.Size);
                            if (rect.IsEmpty)
                            {
                                rect = entity.BaseRect;
                            }
                            else
                            {
                                if (entity.ShadowForm?.IsShowTitle == true)
                                {
                                    rect.Y += 35;
                                    rect.Height -= 35;
                                }
                            }
                            rect.Width -= 5;
                            rect.Height -= 5;
                            if (isOcr)
                            {
                                CommonMethod.DetermineCall(this, delegate
                                {
                                    entity.ShadowForm.Opacity = 0;
                                    Update();
                                });
                            }
                            image = Screenshot.CaptureRectangle(rect, rect);
                            if (isOcr && !isFixShowResultInMainWindow)
                            {
                                CommonMethod.DetermineCall(this, delegate
                                {
                                    entity.ShadowForm.Opacity = 1;
                                    //nowShadowContent.BindImage(image);
                                    Update();
                                });
                            }
                        }
                        if (image != null && lastImage != null && ImageProcessHelper.IsImagesEqual(image, lastImage))
                        {
                            //Console.WriteLine(string.Format("{0} 一样", DateTime.Now.ToString("HH:mm:ss fff")));
                            nowLoopTime--;
                            Thread.Sleep(500);
                        }
                        else
                        {
                            CommonMethod.DetermineCall(this, delegate
                            {
                                entity.ShadowForm.Text = string.Format("{2} 第{0}/{1}次".CurrentText()
                                    , nowLoopTime, entity.LoopTimes
                                    , entity.Item.AccessibleDescription.Replace("固定区域", "").CurrentText());
                            });
                            if (lastOcrTick > 0)
                            {
                                var lastToNowMilSec = entity.IntervalMilSecond - new TimeSpan(ServerTime.DateTime.Ticks - lastOcrTick).TotalMilliseconds;
                                if (lastToNowMilSec > 0)
                                {
                                    Thread.Sleep((int)lastToNowMilSec);
                                }
                            }
                            else
                            {
                                //第一次
                                if (isOcr)
                                {
                                    if (image != null)
                                    {
                                        // 使用优化后的SetPicImage方法
                                        SetPicImage(new Bitmap(image), null, true);
                                    }
                                    var empty = GetEmptyContent("");
                                    empty.result.verticalText = "{}";
                                    BindFixCapture(empty);
                                }
                            }
                            if (image != null)
                            {
                                lastOcrTick = ServerTime.DateTime.Ticks;
                                if (isOcr)
                                {
                                    ProcessByImage(entity.OcrType, ProcessBy.固定区域, image, false);
                                }
                                else
                                {
                                    CommonMethod.SaveImage(image, true, false);
                                }
                            }
                            else
                            {
                                nowLoopTime--;
                            }
                            if (nowLoopTime < entity.LoopTimes)
                            {
                                Thread.Sleep(500);
                            }
                        }
                        lastImage = image;
                    }
                    else
                    {
                        Thread.Sleep(1000);
                    }

                entity.ShadowForm?.Close();
                entity.ShadowForm?.Dispose();
                entity.ShadowForm = null;
            });
        }

        private ShadowForm InitShadowForm(ToolStripItem parentItem, bool isBorder, bool isOcr, bool isShowTitle, Size size, Point location)
        {
            var shadowForm = new ShadowForm
            {
                Size = size,
                IsShowTitle = isShowTitle,
                Location = location,
                StartPosition = FormStartPosition.Manual,
                BackColor = Color.FromArgb(255, 255, 254),
                TransparencyKey = Color.FromArgb(255, 255, 254),
                TopMost = true,
                Icon = Icon,
                Text = parentItem?.AccessibleDescription.CurrentText(),
                ShowInTaskbar = false,
                IsSharkWindow = false,
                ShadowWidth = 10,
                MinimizeBox = false,
                MaximizeBox = false
            };
            if (!isBorder)
            {
                shadowForm.IsShowTitle = false;
                shadowForm.ShadowWidth = 0;
            }
            if (parentItem != null)
            {
                var item = new ToolStripMenuItem
                {
                    AccessibleDescription = parentItem.Text,
                    Text = parentItem.Text.CurrentText(),
                    Image = parentItem.Image,
                    Tag = parentItem.Tag,
                    AccessibleDefaultActionDescription = parentItem.AccessibleDefaultActionDescription
                };
                item.Click += (obj, e) => { parentItem.PerformClick(); };

                var contentMenu = new ContextMenuStrip();
                contentMenu.Items.Add(item);
                shadowForm.ContextMenuStrip = contentMenu;
            }

            var label = new Label()
            {
                BackColor = Color.Transparent,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = CommonSetting.Get默认文本字体(),
                ForeColor = CommonSetting.Get默认文字颜色()
            };
            label.TextChanged += (obj, e) =>
            {
                label.Visible = !string.IsNullOrEmpty(label.Text);
                //try
                //{
                //    if (shadowForm.IsShowTitle != label.Visible)
                //    {
                //        shadowForm.IsShowTitle = label.Visible;
                //        shadowForm.Invalidate();
                //    }
                //}
                //catch
                //{
                //}
            };
            shadowForm.Controls.Add(label);
            var txtContent = new UcContent()
            {
                Dock = DockStyle.Fill,
                Padding = Padding.Empty,
                Margin = Padding.Empty,
                Visible = false,
                IsCanMax = false
            };
            shadowForm.Controls.Add(txtContent);

            string strStop = "停止".CurrentText();
            shadowForm.DoubleClick += (sender, e) =>
            {
                if (parentItem != null && parentItem.Text.StartsWith(strStop))
                    parentItem.PerformClick();
            };
            shadowForm.FormClosed += (sender, e) =>
            {
                if (parentItem != null && parentItem.Text.StartsWith(strStop))
                    parentItem.PerformClick();
            };
            shadowForm.Show();
            return shadowForm;
        }

        private UcContent nowShadowContent;
        private bool isFixShowResultInMainWindow;

        private void SetShadowFormContent(ShadowForm shadowForm)
        {
            try
            {
                nowShadowContent = null;
                if (shadowForm?.Visible == true && shadowForm.Controls.Count > 0)
                    foreach (var control in shadowForm.Controls)
                    {
                        if (control is UcContent ucContent)
                        {
                            nowShadowContent = ucContent;
                            ucContent.SpiltModel = NowSpiltModel;
                            ucContent.IsShowOldContent = false;
                            ucContent.NowDisplayMode = DisplayModel.图文模式;
                            ucContent.IsShowToolBox = false;
                            break;
                        }
                    }
            }
            catch
            {
            }
        }

        private void SetShadowFormText(ShadowForm shadowForm, string text = "", Font font = null)
        {
            try
            {
                if (shadowForm == null || shadowForm.Visible == false || shadowForm.Controls.Count <= 0)
                    return;
                foreach (var control in shadowForm.Controls)
                {
                    if (control is Label lbl)
                    {
                        lbl.Text = text;
                        lbl.Font = font ?? CommonSetting.Get默认文本字体();
                        Update();
                    }
                }
            }
            catch
            {
            }
        }

        private void SetShadowFormTextTick(ShadowForm shadowForm, int second)
        {
            try
            {
                if (shadowForm == null || shadowForm.Visible == false || shadowForm.Controls.Count <= 0)
                    return;
                if (shadowForm.Controls[0] is Label lbl)
                {
                    lbl.TextAlign = ContentAlignment.MiddleCenter;
                    lbl.Text = second.ToString();
                    Update();
                    var thread = new Thread(() =>
                    {
                        int loopTime = 5;
                        int loopPerSize = 20;
                        var isDouble = second % 2 != 0;
                        var baseFontSize = isDouble ? loopTime * loopPerSize : 20;
                        for (int i = 4; i > 0; i--)
                        {
                            try
                            {
                                lbl.Font = CommonString.GetSysBoldFont(baseFontSize +
                                                                       (isDouble ? -1 : 1) * i * loopPerSize);
                            }
                            catch
                            {
                                break;
                            }

                            Thread.Sleep(100);
                        }
                    });
                    thread.Start();
                }
            }
            catch
            {
            }
        }

        private CommonMethod.CaptureImageContent CaptureImageByType(bool isEdit, string operate = null)
        {
            CommonMethod.CaptureImageContent img = null;
            cmsNotify.Hide();
            if (StaticValue.IsCatchScreen)
            {
                CommonMethod.ShowHelpMsg("处理中".CurrentText() + "," + CommonString.StrPleaseWait.CurrentText());
                return img;
            }

            if (!StaticValue.IsCatchScreen)
            {
                var isShowTool = HideFormTool(true);

                img = CommonMethod.CatchImage(string.IsNullOrEmpty(operate) ? NowOcrType + "识别" : operate);

                if (CommonMethod.IsImageEditModel(operate) && img?.Image != null)
                {
                    var lastRect = img.Rectangle;
                    img = CommonMethod.EditImage(img, RegionCaptureMode.Editor);
                    if (img != null)
                    {
                        img.Rectangle = lastRect;
                    }
                    else
                    {
                    }
                }
                if (isShowTool) FrmTool.Visible = true;
            }

            return img;
        }

        private void OcrProcessThread()
        {
            new Thread(p =>
                {
                    try
                    {
                        foreach (var processEntity in OcrPoolProcess.OcrProcessPool.GetConsumingEnumerable())
                            try
                            {
                                if (processEntity.IsLongImage)
                                {
                                    ProcessByLongImage(processEntity.Byts, "长截图".CurrentText());
                                }
                                else
                                {
                                    if (CommonString.IsOnLine
                                    || !Equals(CommonSetting.识别策略, OcrModel.仅网络识别.ToString()) && CommonSetting.启用本地识别)
                                    {
                                        OcrProcessMethod(processEntity);
                                    }
                                    else
                                        CommonMethod.ShowHelpMsg(CommonString.StrNetWorkError.CurrentText());
                                    if (Equals(processEntity.ProcessBy, ProcessBy.主界面) ||
                                        (Equals(processEntity.ProcessBy, ProcessBy.固定区域) && isFixShowResultInMainWindow))
                                        if (!CommonSetting.识别时不显示主界面)
                                            ShowWindow();
                                }
                            }
                            catch (Exception oe)
                            {
                                Log.WriteError("OcrProcessThread-1", oe);
                            }
                    }
                    catch (Exception oe)
                    {
                        Log.WriteError("OcrProcessThread-2", oe);
                    }
                })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        }

        private void CheckUpdate(object sender, EventArgs e)
        {
            CommonUpdate.UpdateMain();
        }

        private void OcrResultProcessThread()
        {
            new Thread(p =>
                {
                    try
                    {
                        bool isFirst = true;
                        var lastId = string.Empty;
                        foreach (var ocrResult in OcrResultPool.GetConsumingEnumerable())
                            try
                            {
                                CommonMethod.DetermineCall(this, delegate
                                {
                                    //Console.WriteLine("识别完成：" + ocrResult.Identity + ",结果：" + CommonString.JavaScriptSerializer.Serialize(ocrResult));

                                    if (Equals(ocrResult.processName, CommonString.StrReminder))
                                    {
                                        if (ocrResult.result?.autoText.Contains("升级到最新版") == true)
                                            CheckUpdate(null, null);
                                        if (ocrResult.result?.autoText.Contains("重新登录") == true)
                                            CheckLoginStatus();
                                    }
                                    if (Equals(ocrResult.ProcessBy, ProcessBy.批量识别))
                                    {
                                        FrmBatchOCR.OcrResultPool.Add(ocrResult);
                                    }
                                    else if (Equals(ocrResult.ProcessBy, ProcessBy.主界面)
                                        || Equals(ocrResult.ProcessBy, ProcessBy.固定区域)
                                        || Equals(ocrResult.ProcessBy, ProcessBy.划词翻译))
                                    {
                                        if (Equals(ocrResult.ProcessBy, ProcessBy.划词翻译))
                                        {
                                            BindToolSearch(ocrResult);
                                        }
                                        else if (Equals(ocrResult.ProcessBy, ProcessBy.固定区域) && !isFixShowResultInMainWindow)
                                        {
                                            BindFixCapture(ocrResult);
                                        }
                                        else
                                        {
                                            BindResult(ocrResult);
                                        }
                                        if (ocrResult.result != null && CommonSetting.自动复制结果到粘贴板 && ocrResult.result.HasResult && ocrResult.result.resultType == ResutypeEnum.文本)
                                        {
                                            isFirst = string.IsNullOrEmpty(lastId) || !Equals(lastId, ocrResult.id);
                                            var model = CommonSetting.ConvertToEnum<CopyResultEnum>(CommonSetting.复制模式);
                                            var txtThis = Equals(ocrResult.state, OcrProcessState.处理成功) ? ocrResult.result.GetTextResult(CommonSetting.首行缩进, false
                                                , CommonSetting.翻译时复制原文, SpiltMode.自动分段
                                                , Equals(ocrResult.ocrType, OcrType.翻译), ocrResult.processName)
                                                : string.Empty;
                                            if (!string.IsNullOrEmpty(txtThis))
                                            {
                                                if (!isFirst)
                                                {
                                                    var txtOld = ClipboardService.GetText();
                                                    if (!string.IsNullOrEmpty(txtOld))
                                                    {
                                                        if (Equals(model, CopyResultEnum.最快))
                                                            txtThis = string.Empty;
                                                        else if (Equals(model, CopyResultEnum.所有))
                                                            txtThis = string.Format("{0}\n\n{1}", txtOld, txtThis)
                                                                .TrimStart();
                                                    }
                                                }

                                                if (!string.IsNullOrEmpty(txtThis))
                                                {
                                                    lastId = ocrResult.id;
                                                    if (!Equals(ocrResult.ProcessBy, ProcessBy.固定区域))
                                                    {
                                                        try
                                                        {
                                                            ClipboardService.SetText(txtThis);
                                                            if (CommonSetting.复制成功后提示)
                                                                CommonMethod.ShowHelpMsg(ocrResult.processName + " " + "识别结果已复制！".CurrentText(), 1000);
                                                        }
                                                        catch
                                                        {
                                                            CommonMethod.ShowHelpMsg("复制识别结果失败！".CurrentText(), 1000);
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                });
                            }
                            catch (Exception oe)
                            {
                                Log.WriteError("OcrResultProcessThread", oe);
                            }
                    }
                    catch (Exception oe)
                    {
                        Console.WriteLine(oe.Message);
                    }
                })
            { IsBackground = true, Priority = ThreadPriority.Highest }.Start();
        }

        private void tsmSearch_Click(object sender, EventArgs e)
        {
            var txt = GetCurrentTxt();
            if (!string.IsNullOrEmpty(txt)) DoSearch(txt);
        }

        public static void DoSearch(string text)
        {
            var url = string.Format(NowSearchEngine.Url, HttpUtility.UrlEncode(text));
            CommonMethod.OpenUrl(url);
        }

        private void OcrProcessMethod(OcrProcessEntity processEntity)
        {
            try
            {
                var isOnMainFormProcess = Equals(processEntity.ProcessBy, ProcessBy.主界面)
                                          || (Equals(processEntity.ProcessBy, ProcessBy.固定区域) && isFixShowResultInMainWindow);
                var isOnHuaCi = Equals(processEntity.ProcessBy, ProcessBy.划词翻译);

                var isFixCaptureShowPic = Equals(processEntity.ProcessBy, ProcessBy.固定区域) && !isFixShowResultInMainWindow;

                if (processEntity.IsShowLoading)
                {
                    if (isOnHuaCi)
                        frmOCR?.ShowLoading(StrProcessOcr.CurrentText() + "," + CommonString.StrPleaseWait.CurrentText(), processEntity.IsShowLoading);
                    else if (isOnMainFormProcess && !Equals(processEntity.ProcessBy, ProcessBy.固定区域))
                        ucLoading1.ShowLoading(StrProcessOcr.CurrentText() + "," + CommonString.StrPleaseWait.CurrentText(), processEntity.IsShowLoading);
                }
                if (isOnMainFormProcess) CloseAllTabs();

                var ocrTask = Task.Factory.StartNew(() =>
                {
                    Thread.CurrentThread.Priority = ThreadPriority.Highest;
                    var ocr = GetOcrContentByBytes(processEntity);
                    return ocr;
                }, CancellationToken.None, TaskCreationOptions.LongRunning, TaskScheduler.Default)
                    .ContinueWith(task =>
                    {
                        var ocr = task.Result;
                        if (string.IsNullOrEmpty(ocr?.id))
                        {
                            if (isOnHuaCi)
                                frmOCR.Tag = null;
                            else if (isOnMainFormProcess)
                                tbMain.Tag = null;
                            return;
                        }

                        if (isOnHuaCi)
                            frmOCR.Tag = ocr.id;
                        else if (isOnMainFormProcess)
                            tbMain.Tag = ocr.id;
                        var hasResult = ocr?.result?.HasResult == true;
                        if (hasResult)
                        {
                            if (processEntity.IsSearch && !string.IsNullOrEmpty(ocr.result.GetAutoText()))
                                DoSearch(ocr.result.GetAutoText());
                            else if (processEntity.OcrType == OcrType.翻译 && ocr.result.IsTransResult &&
                                     processEntity.FileExt.Equals(CommonString.StrDefaultTxtType))
                            {
                                try
                                {
                                    ocr.result.spiltText = Encoding.UTF8.GetString(processEntity.Byts);
                                }
                                catch (Exception oe)
                                {
                                    Console.WriteLine("处理翻译原文出错！".CurrentText() + oe.Message);
                                }
                            }
                            ocr.Identity = processEntity.Identity;
                            ocr.ProcessBy = processEntity.ProcessBy;
                            OcrResultPool.Add(ocr);
                        }

                        if (!isFixCaptureShowPic)
                        {
                            if (ocr != null && !Equals(CommonSetting.识别策略, OcrModel.仅本地识别.ToString()))
                                LoadOtherOcrResult(ocr.id, ocr.Server, processEntity.ProcessBy, processEntity.Identity,
                                hasResult);
                        }
                    })
                    .ContinueWith(task =>
                    {
                        if (isOnHuaCi)
                        {
                            frmOCR?.CloseLoading();
                        }
                        else if (isOnMainFormProcess)
                        {
                            ucLoading1.CloseLoading();
                            if (ActiveForm != this) FlashWindowHelper.Flash(3, 600, Handle);
                        }

                        processEntity.Byts = null;
                        processEntity = null;
                    });
            }
            catch (Exception oe)
            {
                Log.WriteError("OcrProcessMethod", oe);
            }
        }

        private OcrContent GetEmptyContent(string msg)
        {
            return new OcrContent
            {
                id = Guid.NewGuid().ToString(),
                result = new ResultEntity
                {
                    autoText = msg,
                    spiltText = msg
                },
                processName = CommonString.StrReminder
            };
        }

        private OcrContent GetOcrContentByBytes(OcrProcessEntity processEntity)
        {
            processEntity.FileExt = string.IsNullOrEmpty(processEntity.FileExt)
                ? CommonString.StrDefaultImgType
                : processEntity.FileExt;

            // 处理OcrType
            if (CheckIsForbidOperate(processEntity.OcrType)
                || !CommonString.IsValidateFileExt(processEntity.FileExt)
                || ((Program.NowUser == null || Program.NowUser.IsSupportDocFile == false) &&
                 CommonString.IsValidateDocFileExt(processEntity.FileExt))
                 || (Program.NowUser?.IsSupportImageFile == false &&
                 CommonString.IsValidateImageFileExt(processEntity.FileExt))
            )
            {
                return GetEmptyContent("当前用户不支持此类型文件识别！".CurrentText());
            }

            if (processEntity.Byts != null && !string.IsNullOrEmpty(processEntity.FileExt) && CommonString.IsValidateImageFileExt(processEntity.FileExt))
            {
                if (!ImageHelper.IsImageSafe(processEntity))
                {
                    return GetEmptyContent("图片不合规，请遵纪守法！".CurrentText());
                }

                if (OcrHelper.ImagePreProcess(processEntity))
                {
                    SetPicImage(ImageProcessHelper.ByteToImage(processEntity.Byts), processEntity.ImgUrl);
                }

                OcrHelper.QuantizeOcrProcessEntity(processEntity);
            }

            if (!processEntity.GroupType.Contains(0) && (Program.NowUser == null || Program.NowUser.IsSupportPassage == false))
            {
                processEntity.GroupType = new List<int>() { 0 };
            }

            var lstOcrEntity = new List<OcrProcessEntity>();
            if (!Equals(CommonSetting.识别策略, OcrModel.仅网络识别.ToString())
                && CommonSetting.启用本地识别
                && !processEntity.IsLocal)
            {
                if (processEntity.OcrType == OcrType.文本 || processEntity.OcrType == OcrType.竖排 || processEntity.OcrType == OcrType.表格)
                    processLocalOcrEntity(processEntity, lstOcrEntity);

                if (lstOcrEntity.Count <= 0)
                {
                    if (Equals(CommonSetting.识别策略, OcrModel.仅本地识别.ToString()))
                    {
                        CommonMethod.ShowHelpMsg("识别策略自动切换为 本地加网络 模式！".CurrentText());
                        CommonSetting.识别策略 = OcrModel.本地加网络.ToString();
                    }
                }
            }
            if (processEntity.IsLocal || !Equals(CommonSetting.识别策略, OcrModel.仅本地识别.ToString()))
            {
                lstOcrEntity.Add(processEntity);
            }
            var waitEntity = lstOcrEntity.FirstOrDefault(p => !p.IsLocal) ?? lstOcrEntity.FirstOrDefault(p => p.IsLocal);
            if (waitEntity != null)
            {
                lstOcrEntity.Remove(waitEntity);
            }
            if (lstOcrEntity.Count > 0)
            {
                lstOcrEntity.AsParallel().Select(p => Task.Factory.StartNew(() =>
                  {
                      var result = OcrHelper.GetLocalOcrResult(p);
                      if (result?.result != null && result.result.HasResult)
                      {
                          if (p.AddToPool)
                          {
                              OcrResultPool.Add(result);
                              ucLoading1.CloseLoading();
                          }
                      }
                  })).ToArray();
            }
            OcrContent ocr = null;
            if (waitEntity != null)
            {
                ocr = waitEntity.IsLocal ? OcrHelper.GetLocalOcrResult(waitEntity) : OcrHelper.GetResult(waitEntity);
                waitEntity.Byts = null;
            }
            lstOcrEntity.ForEach(p => { p.Byts = null; });
            lstOcrEntity = null;
            return ocr;
        }

        private void processLocalOcrEntity(OcrProcessEntity processEntity, List<OcrProcessEntity> lstOcrEntity)
        {
            var fileIdentity = Guid.NewGuid().ToString();
            var lstType = CommonSetting.GetListValue(CommonSetting.LocalOcrConfigKey, 0).Where(p => p > 0).Distinct();
            if (lstType?.Count() > 0)
            {
                foreach (var ocrType in lstType)
                {
                    OcrProcessEntity localEntity = ObjectCopy<OcrProcessEntity, OcrProcessEntity>.Trans(processEntity);
                    localEntity.IsLocal = true;
                    localEntity.LocalOcrType = ocrType;
                    localEntity.Identity = fileIdentity;
                    localEntity.OcrType = processEntity.OcrType;
                    lstOcrEntity.Add(localEntity);
                }
            }
            else
            {
                CommonSetting.启用本地识别 = false;
                CommonMethod.ShowHelpMsg("本地识别已自动停用，请在设置->识别->本地识别 中配置！".CurrentText());
            }
        }

        private void ocrSpiltModelItem_Click(object sender, EventArgs e)
        {
            var clickedItem = sender as ToolStripMenuItem;
            if (clickedItem == null) return;
            foreach (ToolStripMenuItem item in tmsSpiltMode.DropDownItems) item.Checked = Equals(item, clickedItem);
            var selectedGroupType = (SpiltMode)BoxUtil.GetInt32FromObject(clickedItem.Tag?.ToString());
            if (!selectedGroupType.Equals(NowSpiltModel))
            {
                BindSpiltModel(selectedGroupType);
                CommonSetting.SetValue("分段模式", NowSpiltModel.ToString());
            }

            //tsmDDL_DropDownItemClicked("OCR分组", new ToolStripItemClickedEventArgs(clickedItem));
            //(sender as ToolStripMenuItem)?.OwnerItem?.PerformClick();
        }

        private void ocrGroupTypeItem_Click(object sender, EventArgs e)
        {
            var clickedItem = sender as ToolStripMenuItem;
            if (clickedItem == null) return;

            var newType = BoxUtil.GetInt32FromObject(clickedItem.Tag?.ToString(), 0);
            if (!OcrHelper.LstServerOcrGroup.Any(p => Equals(p.Code, newType))) return;

            if (newType == 0)
            {
                NowOcrGroupType = new List<int>() { 0 };
            }
            else
            {
                NowOcrGroupType.Remove(0);

                if (NowOcrGroupType.Contains(newType))
                {
                    NowOcrGroupType.Remove(newType);
                    if (NowOcrGroupType.Count <= 0)
                    {
                        NowOcrGroupType = new List<int>() { 0 };
                    }
                }
                else
                {
                    if (Program.NowUser == null || !Program.NowUser.IsSupportPassage)
                    {
                        NowOcrGroupType = new List<int>() { 0 };
                    }
                    else
                    {
                        if (NowOcrGroupType.Count >= Program.NowUser.MaxPassageCount)
                        {
                            NowOcrGroupType.Clear();
                        }
                        NowOcrGroupType.Add(newType);
                    }
                }
            }


            // Update the checked state of the menu items
            foreach (ToolStripMenuItem item in ocrGroupTypeToolStripMenuItem.DropDownItems)
            {
                var itemType = BoxUtil.GetInt32FromObject(item.Tag?.ToString(), 0);
                item.Checked = NowOcrGroupType.Contains(itemType);
            }

            // Save the selected group types to settings
            CommonSetting.SetValue("识别引擎", string.Join(",", NowOcrGroupType));
        }

        private void ocrTypeItem_Click(object sender, EventArgs e)
        {
            tsmDDL_DropDownItemClicked(tsmContentType,
                new ToolStripItemClickedEventArgs(sender as ToolStripMenuItem));
            (sender as ToolStripMenuItem)?.OwnerItem?.PerformClick();
        }

        #region 图片预览保存

        /// <summary>
        /// 优化后的图像设置方法 - 使用BeginInvoke和布局优化
        /// </summary>
        private void SetPicImage(Image img, string imgUrl = null, bool isSetParent = true)
        {
            // 使用BeginInvoke而不是Invoke，避免阻塞调用线程
            this.BeginInvoke(new Action(() =>
            {
                // 暂停布局计算，避免多次重绘
                this.SuspendLayout();
                try
                {
                    if (img != null)
                        img.Tag = imgUrl;

                    // 优化图像绑定
                    ImageBox.BindImage(img, !isSetParent, CommonSetting.图片自动缩放);

                    if (isSetParent)
                    {
                        if (img == null)
                            tbImageBox.Parent = null;
                        else
                            tbImageBox.Parent = tbMain;
                    }
                }
                finally
                {
                    // 恢复布局计算，一次性完成所有布局
                    this.ResumeLayout(true);
                }
            }));
        }

        #endregion

        private string GetCurrentTxt(bool isAll = true)
        {
            foreach (Control item in tbMain.SelectedTab.Controls)
                if (item is UcContent ucContent)
                {
                    return ucContent.GetContentText(isAll);
                }

            return null;
        }

        private void UpdatePicViewModel(DisplayModel model)
        {
            tsmPicViewModel.Image = ProcessStyleImage(tsmPicViewModel.SetResourceImage(model.ToString()));
            tsmPicViewModel.Text = string.Format("【{0}】", model.ToString().CurrentText());
            tmsSpiltMode.Visible = NowOcrType != OcrType.翻译 && NowOcrType != OcrType.表格 && NowOcrType != OcrType.公式;
            var lstControl = GetNowBindUserControls();
            lstControl.ForEach(p =>
            {
                p.NowDisplayMode = model;
            });
        }

        private void ShowContentText(string txtContent, bool isAppend = false)
        {
            content.BindContentByStr(txtContent, isAppend);
        }

        private void CheckNetWork()
        {
            tbMain.SelectedIndex = 0;

            ShowContentText("【网络检测】".CurrentText());
            ShowContentText("1、本地连接状态检测…".CurrentText(), true);
            SetNowNetWork(CommonString.IsOnLine, true);
            var error = !CommonString.IsOnLine;
            ShowContentText("本地网络连接".CurrentText() + (error ? "异常".CurrentText() : "正常".CurrentText()), true);
            ShowContentText("", true);

            ShowContentText("2、开始上网检测…".CurrentText(), true);
            error = !DnsHelper.Init();
            ShowContentText("获取助手服务器".CurrentText() + (error ? "异常".CurrentText() : "正常".CurrentText()), true);
            var lstType = OcrHelper.GetCanRegUserTypes();
            error = !(lstType != null && lstType.Count > 0);
            ShowContentText("访问助手服务器".CurrentText() + (error ? "异常".CurrentText() : "正常".CurrentText()), true);
            var html = WebClientExt.GetHtml("http://www.baidu.com");
            error = string.IsNullOrEmpty(html);
            ShowContentText("访问外部网络".CurrentText() + (error ? "异常".CurrentText() : "正常".CurrentText()), true);
            ShowContentText("", true);

            ShowContentText("网络检测完毕！".CurrentText(), true);
        }

        private void pnlNetWork_Click(object sender, EventArgs e)
        {
            Task.Factory.StartNew(() =>
            {
                ShowWindow();
                CheckNetWork();
            });
        }

        #region 检查更新相关

        private void CheckLoginStatus()
        {
            try
            {
                if (DateTime.Now.Minute % 5 == 0)
                    Log.TrackEvent(string.Format("Heart {0}ID:{1}"
                        , !string.IsNullOrEmpty(CommonSetting.用户名) ? "User:" + CommonSetting.用户名 + "," : ""
                        , CommonMethod.Identity));
                if (OcrHelper.IsLogouted())
                {
                    Program.NowUser = null;
                    DoLoginSilence(null, null);
                }
            }
            catch (Exception oe)
            {
                Console.WriteLine(oe.Message);
            }
        }

        #endregion

        #region MouseHook

        private void mouseHook_MouseSelected(object sender, MouseEventArgs e)
        {
            mouseHook_DoubleClick(sender, null);
        }

        private FormOcr frmOCR;

        private void HookManager_MouseClick(object sender, MouseEventArgs e)
        {
            //if (frmSearch?.IsLeave == false)
            //{
            //    return;
            //}
            if (!CommonString.IsOnOcrButton && !CommonString.IsOnOcrSearch) frmOCR?.HideWindow(true);
        }

        private readonly List<string> lstAllowCopyControls = new List<string>
            {"Pane", "Document", "Edit", "ListItem", "Text", "Window"};

        private readonly List<string> lstNotAllowCopyClasses = new List<string> { "IHWindowClass" };

        private object GetFocusElementText(out ClipboardContentType contentType)
        {
            object result = null;

            contentType = ClipboardContentType.文本;
            var isFile = false;
            var isImg = false;
            var mouse = Cursor.Position; // use Windows forms mouse code instead of WPF
            var element = AutomationElement.FromPoint(new System.Windows.Point(mouse.X, mouse.Y));

            if (element != null)
            {
                var controlName = element.Current.ControlType?.ProgrammaticName;
                var className = element.Current.ClassName;
                //隐藏元素或者密码类，不捕获
                if (element.Current.IsPassword || element.Current.IsOffscreen) return result;
                result = element.GetText(out var isIn);
                if (!isIn)
                {
                    if (!string.IsNullOrEmpty(className) && lstNotAllowCopyClasses.Any(p => className.Contains(p)))
                        return result;
                    if (string.IsNullOrEmpty(controlName) || (!string.IsNullOrEmpty(controlName) && lstAllowCopyControls.Any(p => controlName.Contains(p))))
                        CommonMethod.DetermineCall(this, delegate
                        {
                            var oldText = ClipboardService.GetText()?.Trim();
                            var oldDataBackup = ClipboardService.Backup();
                            try
                            {
                                //开始复制
                                SendKeys.SendWait("^c");
                                SendKeys.Flush();
                                Thread.Sleep(100);
                                result = ClipboardService.GetText()?.Trim();
                                if (result == null)
                                {
                                    //result = ClipboardService.GetImage();
                                    //if (result != null)
                                    //{
                                    //    isImg = true;
                                    //}
                                    //else
                                    //{
                                    //    result = ClipboardService.GetOneFile();
                                    //    if (result != null && !string.IsNullOrEmpty(result.ToString()))
                                    //    {
                                    //        isFile = true;
                                    //    }
                                    //}
                                }
                                else
                                {
                                    if (Equals(result, oldText)) result = null;
                                }
                            }
                            catch (Exception oe) { }
                            finally
                            {
                                ClipboardService.Restore(oldDataBackup);
                            }
                        });
                }
            }

            if (isFile)
                contentType = ClipboardContentType.文件;
            else if (isImg) contentType = ClipboardContentType.图片;
            return result;
        }

        private void mouseHook_DoubleClick(object sender, EventArgs e)
        {
            if (CommonString.IsOnOcrButton || CommonString.IsOnOcrSearch) return;
            Task.Factory.StartNew(() =>
            {
                try
                {
                    var result = GetFocusElementText(out var contentType);

                    var location = new Point(Cursor.Position.X + 10, Cursor.Position.Y - 15 - 32);
                    ShowMiniSearch(result?.ToString(), contentType, location);
                }
                catch
                {
                }
            });
        }

        private void ShowMiniSearch(string result, ClipboardContentType contentType, Point location)
        {
            try
            {
                if (string.IsNullOrEmpty(result)) return;
                //frmSearch?.Hide();
                CommonMethod.DetermineCall(this, delegate
                {
                    if (frmOCR == null) frmOCR = new FormOcr { Icon = Icon };
                    frmOCR.Content = result;
                    frmOCR.ContentType = contentType;

                    if (location != Point.Empty)
                    {
                        frmOCR.Location = location;
                        frmOCR.ShowTool(true);
                        if (!Equals(frmOCR.Width, 32)) frmOCR.Size = new Size(32, 32);
                        if (!Equals(location, frmOCR.Location)) frmOCR.Location = location;
                    }
                    else
                    {
                        frmOCR.ShowSearch();
                    }
                    Update();
                });
                //Clipboard.Clear();
            }
            catch
            {
            }
        }

        #endregion

        #region 权限相关

        private bool CheckIsForbidOperate(OcrType ocrType)
        {
            var isForbid = false;
            if (Equals(ocrType, OcrType.竖排) && (Program.NowUser == null || !Program.NowUser.IsSupportVertical)
                || Equals(ocrType, OcrType.表格) && (Program.NowUser == null || !Program.NowUser.IsSupportTable)
                || Equals(ocrType, OcrType.公式) && (Program.NowUser == null || !Program.NowUser.IsSupportMath)
                || Equals(ocrType, OcrType.翻译) && (Program.NowUser == null || !Program.NowUser.IsSupportTranslate)
            )
                isForbid = true;

            return isForbid;
        }

        private void ProcessForbidControls()
        {
            try
            {
                //PDF识别StripMenuItem.Visible = (Program.NowUser?.IsSupportDocFile == true);
                批量识别ToolStripMenuItem.Visible = Program.NowUser?.IsSupportBatch == true;
                文件识别ToolStripMenuItem.Visible = Program.NowUser?.IsSupportImageFile == true;

                ocrGroupTypeToolStripMenuItem.Visible = Program.NowUser?.IsSupportPassage == true;
                tsmOcrGroupSpilt.Visible = Program.NowUser?.IsSupportPassage == true;

                InitOcrTypes();
                tbMain.Focus();

                var isSupportImgFile = Program.NowUser?.IsSupportImageFile == true;

                CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.固定区域截图).Visible = isSupportImgFile;
                var fixAreaOcr = CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, "固定区域识别".CurrentText());
                if (fixAreaOcr != null)
                    fixAreaOcr.Visible = isSupportImgFile;
                var fixAreaTrans = CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, "固定区域翻译".CurrentText());
                if (fixAreaTrans != null)
                    fixAreaTrans.Visible = Program.NowUser?.IsSupportTranslate == true;

                RefreshMenuItemShortCur();
            }
            catch (Exception oe)
            {
                Log.WriteError("ProcessForbidControls", oe);
            }
        }

        private void InitOcrTypes()
        {
            tsmContentType.DropDownItems.Clear();
            截图识别toolStripMenuItem.DropDownItems.Clear();
            粘贴识别ToolStripMenuItem.DropDownItems.Clear();
            文件识别ToolStripMenuItem.DropDownItems.Clear();
            foreach (OcrType ocrType in Enum.GetValues(typeof(OcrType)))
                if (!CheckIsForbidOperate(ocrType))
                {
                    var itemText = (ocrType.ToString() + "识别").CurrentText();
                    if (Equals(ocrType, OcrType.翻译))
                    {
                        itemText = "截图翻译".CurrentText();
                    }
                    var item = new ToolStripMenuItem
                    {
                        AccessibleDescription = (ocrType.ToString() + "识别"),
                        Text = itemText,
                        Tag = ocrType.GetHashCode(),
                        Font = cmsNotify.Items[0].Font,
                    };
                    item.Image = item.SetResourceImage(ocrType.ToString());
                    tsmContentType.DropDownItems.Add(item);

                    var 截图item = new ToolStripMenuItem
                    {
                        AccessibleDescription = (ocrType.ToString() + "识别"),
                        Text = itemText,
                        Tag = ocrType.GetHashCode(),
                        Font = cmsNotify.Items[0].Font,
                    };
                    截图item.Image = 截图item.SetResourceImage(ocrType.ToString());
                    截图item.Click += ocrTypeItem_Click;
                    截图识别toolStripMenuItem.DropDownItems.Add(截图item);

                    var 粘贴item = new ToolStripMenuItem
                    {
                        AccessibleDescription = (ocrType.ToString() + "识别"),
                        Text = itemText,
                        Tag = ocrType.GetHashCode(),
                        Font = cmsNotify.Items[0].Font,
                    };
                    粘贴item.Image = 粘贴item.SetResourceImage(ocrType.ToString());
                    粘贴item.Click += ocrTypeItem_Click;
                    粘贴识别ToolStripMenuItem.DropDownItems.Add(粘贴item);

                    var 图片识别item = new ToolStripMenuItem
                    {
                        AccessibleDescription = (ocrType.ToString() + "识别"),
                        Text = itemText,
                        Tag = ocrType.GetHashCode(),
                        Font = cmsNotify.Items[0].Font,
                    };
                    图片识别item.Image = 图片识别item.SetResourceImage(ocrType.ToString());
                    图片识别item.Click += ocrTypeItem_Click;
                    文件识别ToolStripMenuItem.DropDownItems.Add(图片识别item);
                }

            var 搜索item = new ToolStripMenuItem
            {
                AccessibleDescription = "截图搜索",
                Text = "截图搜索".CurrentText(),
                Tag = "搜索",
                Font = cmsNotify.Items[0].Font,
            };
            搜索item.Image = 搜索item.SetResourceImage("搜索");
            搜索item.Click += ocrTypeItem_Click;
            截图识别toolStripMenuItem.DropDownItems.Add(搜索item);
        }

        private void InitOcrGroupItems()
        {
            OcrHelper.InitOcrGroup();

            BeginInvoke(new Action(() =>
            {
                ocrGroupTypeToolStripMenuItem.DropDownItems.Clear();
                foreach (var keyValuePair in OcrHelper.LstServerOcrGroup)
                {
                    var item = new ToolStripMenuItem
                    { Text = keyValuePair.Name.CurrentText(), Tag = keyValuePair.Code, Font = cmsNotify.Items[0].Font, AccessibleDescription = keyValuePair.Name, AccessibleName = keyValuePair.Name };
                    item.Click += ocrGroupTypeItem_Click;
                    if (NowOcrGroupType.Contains(keyValuePair.Code)) item.Checked = true;
                    ocrGroupTypeToolStripMenuItem.DropDownItems.Add(item);
                }
                Task.Factory.StartNew(() =>
                {
                    foreach (ToolStripMenuItem item in ocrGroupTypeToolStripMenuItem.DropDownItems)
                    {
                        try
                        {
                            var url = string.Format("{0}ico/{1}.ico", CommonString.HostUpdate?.FullUrl, item.AccessibleName);
                            item.Image = ImageProcessHelper.UrlToBitmap(url);
                        }
                        catch { }
                    }
                });

                ImageBox?.InitImageType();
            }));
        }

        private void InitSearchMenu()
        {
            if (CommonEnumAction<string>.FindMeunItem(cmsNotify, "固定区域识别".CurrentText()) == null)
            {
                var item = new ToolStripMenuItem
                {
                    AccessibleDescription = "固定区域识别",
                    Text = "固定区域识别",
                    Font = cmsNotify.Items[0].Font,
                };
                item.Image = item.SetResourceImage("固定区域识别");
                item.Click += (obj, e) => { FixAreaItemClick(item); };
                识别合集toolStripMenuItem.DropDownItems.Add(item);
            }
            if (CommonEnumAction<string>.FindMeunItem(cmsNotify, "固定区域翻译".CurrentText()) == null)
            {
                var item = new ToolStripMenuItem
                {
                    AccessibleDescription = "固定区域翻译",
                    Text = "固定区域翻译",
                    Font = cmsNotify.Items[0].Font,
                };
                item.Image = item.SetResourceImage("翻译");
                item.Click += (obj, e) => { FixAreaItemClick(item); };
                识别合集toolStripMenuItem.DropDownItems.Add(item);
            }
            {
                var item = new ToolStripMenuItem
                {
                    AccessibleDescription = "识别配置",
                    Text = "识别配置",
                    Font = cmsNotify.Items[0].Font,
                };
                item.Image = item.SetResourceImage("设置");
                item.Click += 系统设置SToolStripMenuItem_Click;
                识别合集toolStripMenuItem.DropDownItems.Add(item);
            }
            {
                var item = new ToolStripMenuItem
                {
                    AccessibleDescription = "快捷键配置",
                    Text = "快捷键配置".CurrentText(),
                    Font = cmsNotify.Items[0].Font,
                };
                item.Image = item.SetResourceImage("keyboard_enter");
                item.Click += 系统设置SToolStripMenuItem_Click;
                识别合集toolStripMenuItem.DropDownItems.Add(item);
            }
        }

        private void InitSpiltModel()
        {
            tmsSpiltMode.DropDownItems.Clear();
            foreach (SpiltMode model in Enum.GetValues(typeof(SpiltMode)))
            {
                var item = new ToolStripMenuItem
                {
                    Text = model.ToString().CurrentText(),
                    Tag = model.GetHashCode(),
                    Font = cmsNotify.Items[0].Font,
                    AccessibleDescription = model.ToString(),
                    //ImageScaling = ToolStripItemImageScaling.None
                    //Name = "cmsSpiltModel" + model.GetHashCode()
                };
                item.Image = item.SetResourceImage(model.ToString());
                item.Click += ocrSpiltModelItem_Click;
                if (model.Equals(NowSpiltModel)) item.Checked = true;
                tmsSpiltMode.DropDownItems.Add(item);
            }
        }

        private void InitDisplayModel()
        {
            tsmPicViewModel.DropDownItems.Clear();
            foreach (DisplayModel model in Enum.GetValues(typeof(DisplayModel)))
            {
                var item = new ToolStripMenuItem
                {
                    Text = model.ToString().CurrentText(),
                    Tag = model.GetHashCode(),
                    Font = cmsNotify.Items[0].Font,
                    AccessibleDescription = model.ToString(),
                    //ImageScaling = ToolStripItemImageScaling.None
                    //Name = "cmsSpiltModel" + model.GetHashCode()
                };
                item.Image = item.SetResourceImage(model.ToString());
                tsmPicViewModel.DropDownItems.Add(item);
            }
        }

        #endregion

        #region 绑定识别结果

        private bool IsClosingTabs;

        private void CloseAllTabs()
        {
            IsClosingTabs = true;
            try
            {
                // CloseAllTabs通常在UI线程中调用，直接执行
                if (InvokeRequired)
                {
                    Invoke(new Action(CloseAllTabs));
                    return;
                }

                tbMain.SuspendLayout();
                this.SuspendLayout();

                try
                {
                    tbMain.Tag = null;
                    content.Tag = null;

                    // 先切换到默认Tab，避免选中已删除的Tab
                    tbMain.SelectedTab = tbContentText;
                    SetDefaultTabContentName(tbContentText);
                    ShowContentText("");

                    var tabsToRemove = new List<TabPage>();
                    for (var i = 0; i < tbMain.TabCount; i++)
                    {
                        var itemPage = tbMain.TabPages[i];
                        if (itemPage != tbContentText && itemPage != tbImageBox)
                        {
                            tabsToRemove.Add(itemPage);
                        }
                    }

                    foreach (var tabPage in tabsToRemove)
                    {
                        DisposeTabControls(tabPage);
                        tbMain.TabPages.Remove(tabPage);
                        tabPage.Dispose();
                    }
                }
                catch (Exception oe)
                {
                    Log.WriteError("CloseAllTabs-Inner", oe);
                }
                finally
                {
                    // 恢复布局计算
                    tbMain.ResumeLayout(true);
                    this.ResumeLayout(true);
                }
            }
            catch (Exception oe)
            {
                Log.WriteError("CloseAllTabs", oe);
            }
            finally
            {
                IsClosingTabs = false;
            }
        }

        /// <summary>
        /// 释放Tab页内控件的资源
        /// </summary>
        private void DisposeTabControls(TabPage tabPage)
        {
            try
            {
                if (tabPage?.Controls != null)
                {
                    // 递归释放所有子控件
                    var controlsToDispose = new List<Control>();
                    foreach (Control ctrl in tabPage.Controls)
                    {
                        controlsToDispose.Add(ctrl);
                    }

                    foreach (var ctrl in controlsToDispose)
                    {
                        if (ctrl is IDisposable disposable)
                        {
                            disposable.Dispose();
                        }
                    }
                    tabPage.Controls.Clear();
                }
            }
            catch (Exception ex)
            {
                Log.WriteError("DisposeTabControls", ex);
            }
        }

        private void LoadOtherOcrResult(string tagId, string server, ProcessBy processBy, string fileIdentity, bool sync = true)
        {
            if (sync)
            {
                Task.Factory.StartNew(() => { GetResultProcess(tagId, server, processBy, fileIdentity); });
            }
            else
            {
                Thread.Sleep(800);
                GetResultProcess(tagId, server, processBy, fileIdentity, true);
            }
        }

        private void GetResultProcess(string tagId, string server, ProcessBy processBy, string fileIdentity,
            bool hasResultBreak = false)
        {
            var loopCount = hasResultBreak ? 20 : 40;
            for (var i = 0; i < loopCount; i++)
            {
                var id = Equals(processBy, ProcessBy.主界面) || Equals(processBy, ProcessBy.固定区域)
                    ? tbMain.Tag as string
                    : Equals(processBy, ProcessBy.划词翻译)
                        ? frmOCR.Tag as string
                        : tagId;
                if (string.IsNullOrEmpty(id) || !id.Equals(tagId)) return;
                var lstResult = OcrHelper.GetResultById(id);
                if (lstResult?.Count > 0)
                {
                    Console.WriteLine(string.Format("id:{0},count:{1}", id, lstResult.Count));
                    if (!hasResultBreak) loopCount = Math.Min(loopCount, i + 3);
                    lstResult.ForEach(ocr =>
                    {
                        ocr.Identity = fileIdentity;
                        ocr.ProcessBy = processBy;
                        OcrResultPool.Add(ocr);
                    });
                }

                if (hasResultBreak && lstResult?.Count > 0)
                {
                    LoadOtherOcrResult(tagId, server, processBy, fileIdentity, true);
                    break;
                }

                if (i == loopCount - 1)
                {
                    if (hasResultBreak && (lstResult == null || lstResult.Count <= 0))
                    {
                        var strError = "识别失败，请稍后重试！".CurrentText();
                        if (!string.IsNullOrEmpty(tagId))
                        {
                            strError += "\nTrace:" + tagId;
                        }
                        if (!string.IsNullOrEmpty(server))
                        {
                            strError += "\nNode:" + server;
                        }
                        var ocr = new OcrContent
                        {
                            id = fileIdentity,
                            Identity = fileIdentity,
                            ProcessBy = processBy,
                            result = new ResultEntity
                            {
                                autoText = strError,
                                spiltText = strError
                            },
                            processName = CommonString.StrReminder
                        };
                        OcrResultPool.Add(ocr);
                    }
                }
                else
                {
                    // 优化：智能轮询间隔，前期频繁轮询，后期降低频率
                    var sleepTime = GetOptimalPollingInterval(i);
                    Thread.Sleep(sleepTime);
                }
            }
        }

        /// <summary>
        /// 获取最优的轮询间隔时间
        /// </summary>
        /// <param name="iteration">当前迭代次数（从0开始）</param>
        /// <returns>间隔时间（毫秒）</returns>
        private int GetOptimalPollingInterval(int iteration)
        {
            // 递增间隔策略：前几次快速轮询，后续逐渐增加间隔
            // 总体比原来的固定1500ms更快，同时减少不必要的网络请求
            if (iteration < 3)
                return 500;  // 前3次：500ms，快速获取结果
            else if (iteration < 8)
                return 1000; // 第4-8次：1000ms，中等频率
            else if (iteration < 15)
                return 1500; // 第9-15次：1500ms，保持原有间隔
            else
                return 2000; // 第16次以后：2000ms，降低频率
        }

        private void BindResult(OcrContent item)
        {
            if (IsClosingTabs) return;
            if (item == null || item.result == null) return;

            var tabTitle = string.Format("【{0}】", item.processName.CurrentText(true));
            var tabId = item.processId.ToString();
            bool isTxtTrans = item.ocrType.Equals(OcrType.翻译)
                              && ImageBox.Image == null
                              && item.result.resultType == ResutypeEnum.文本;

            if (isTxtTrans)
            {
                tabTitle = "【文本翻译】".CurrentText();
                tabId = "TransResult";
                if (BindTransResult(tabId, item)) return;
            }

            CommonMethod.DetermineCallSync(this, () => BindResultInternal(item, tabTitle, tabId, isTxtTrans));
        }

        /// <summary>
        /// 内部绑定结果方法 - 必须在UI线程中调用
        /// </summary>
        private void BindResultInternal(OcrContent item, string tabTitle, string tabId, bool isTxtTrans)
        {
            lock (content)
            {
                // 暂停所有布局更新
                this.SuspendLayout();
                tbMain.SuspendLayout();

                try
                {
                    var isFirstTab = content.Tag == null;
                    TabPage tabPage;
                    UcContent contentCtrl;

                    if (isFirstTab)
                    {
                        tabPage = tbContentText;
                        contentCtrl = content;
                        content.Tag = item.processName;

                        tabPage.Text = tabTitle;
                        tabPage.Name = tabId;
                    }
                    else
                    {
                        tabPage = new TabPage()
                        {
                            Padding = Padding.Empty,
                            Margin = Padding.Empty,
                            Text = tabTitle,
                            Name = tabId
                        };

                        contentCtrl = new UcContent
                        {
                            Dock = DockStyle.Fill,
                            SpiltModel = NowSpiltModel,
                            IsShowOldContent = IsShowOldContent,
                            MenuStrip = content.MenuStrip,
                            ScalingStrategy = ImageScalingStrategy.FitInteractive,
                            TxtKeyDownEventDelegate = TxtContent_KeyDown
                        };

                        tabPage.Controls.Add(contentCtrl);

                        CommonMethod.DetermineCallAsync(this, () =>
                        {
                            LanguageHelper.InitTextInfo(this, new List<Control> { contentCtrl }, true);
                            contentCtrl.SetDragDrop();
                            InitForm.AddNewControl(tabPage);
                        });

                        // 一次性添加到TabControl
                        tbMain.TabPages.Add(tabPage);
                    }

                    // 使用统一的内容绑定方法
                    BindUcContent(contentCtrl, item, isTxtTrans, true);

                    // 最后更新选中状态
                    TbMainSelectedIndexChanged();
                }
                finally
                {
                    tbMain.ResumeLayout(true);
                    this.ResumeLayout(true);
                }
            }
        }

        private void BindToolSearch(OcrContent item)
        {
            var ucContent = frmOCR?.GetUcContent();
            if (ucContent != null)
            {
                ucContent.SpiltModel = NowSpiltModel;
                ucContent.IsShowOldContent = IsShowOldContent;
                //ucContent.ContentBackColor = DefaultTxtColor;
                //ucContent.MenuStrip = content.MenuStrip;

                var isAppend = Equals(item.id, frmOCR.Tag?.ToString());
                BindUcContent(ucContent, item, isAppend, true); // 划词翻译
            }
        }

        private void BindFixCapture(OcrContent item)
        {
            var ucContent = nowShadowContent;
            if (ucContent != null && !ucContent.IsDisposed)
            {
                try
                {
                    ucContent.Visible = true;
                    ucContent.BringToFront();

                    BindUcContent(ucContent, item, false, false);
                }
                catch (Exception oe)
                {
                    Log.WriteError("BindFixCapture", oe);
                }
            }
        }

        private bool BindTransResult(string transKey, OcrContent ocr)
        {
            var result = false;
            var controls = Controls.Find(transKey, true);
            if (controls.Length > 0)
                foreach (var item in controls[0].Controls)
                    if (item is UcContent ucContent)
                    {
                        // 使用统一的绑定方法，翻译结果使用追加模式
                        BindUcContent(ucContent, ocr, true, true);
                        result = true;
                        break;
                    }

            return result;
        }

        private void BindUcContent(UcContent ucContent, OcrContent ocr, bool isAppend, bool isSetModel)
        {
            if (ucContent == null || ocr == null) return;

            CommonMethod.DetermineCallSync(this, () =>
            {
                // 暂停布局，批量处理所有操作
                ucContent.SuspendLayout();
                try
                {
                    // 1. 设置基本属性
                    if (ucContent.Padding != Padding.Empty || ucContent.Margin != Padding.Empty)
                    {
                        ucContent.Padding = Padding.Empty;
                        ucContent.Margin = Padding.Empty;
                    }

                    // 2. 绑定图像（如果需要）
                    if (ocr.IsCanVertical == true && ImageBox.Image != null)
                    {
                        ucContent.BindImage(ImageBox.Image, true, CommonSetting.图片自动缩放);
                    }

                    // 3. 设置显示模式
                    if (isSetModel)
                    {
                        ucContent.NowDisplayMode = NowDisplayMode;
                    }

                    // 4. 刷新样式
                    ucContent.RefreshStyle();

                    // 5. 绑定内容
                    var showTextPreview = Equals(ocr.ProcessBy, ProcessBy.固定区域) || CommonSetting.显示文字预览;
                    ucContent.BindContentByOcr(ocr, isAppend, showTextPreview);
                }
                catch (Exception ex)
                {
                    Log.WriteError("BindUcContent", ex);
                }
                finally
                {
                    // 恢复布局，一次性完成所有更新
                    ucContent.ResumeLayout(true);
                }
            });
        }

        #endregion

        #region 设置相关

        private void tsmShowTool_Click(object sender, EventArgs e)
        {
            FrmTool.VisibleChange();
            CommonSetting.SetValue("显示工具栏", FrmTool.Visible);
        }

        private void tsmShowMain_Click(object sender, EventArgs e)
        {
            if (Visible)
                Visible = false;
            else
                ShowWindow();
        }

        private void NotifyMain_BalloonTipClicked(object sender, EventArgs e)
        {
            if (notifyMain.Tag is BalloonTipAction action && !string.IsNullOrEmpty(action.Text))
                switch (action.ClickAction)
                {
                    case BalloonTipClickAction.OpenUrl:
                        CommonMethod.OpenUrl(action.Text);
                        break;
                    case BalloonTipClickAction.OpenForm:
                        CommonMethod.OpenForm(action.Text);
                        break;
                }
        }

        private bool isoverlapped(IntPtr win, Form form)
        {
            try
            {
                var preWin = NativeMethods.GetWindow(win, 3); //获取显示在Form之上的窗口

                if (preWin == null || preWin == IntPtr.Zero)
                    return false;

                if (!NativeMethods.IsWindowVisible(preWin))
                    return isoverlapped(preWin, form);

                var rect = new RECT();
                if (NativeMethods.GetWindowRect(preWin, out rect)) //获取窗体矩形
                {
                    var winrect = new Rectangle(rect.Left, rect.Top, rect.Right - rect.Left, rect.Bottom - rect.Top);

                    if (winrect.Width == Screen.PrimaryScreen.WorkingArea.Width &&
                        winrect.Y == Screen.PrimaryScreen.WorkingArea.Height) //菜单栏。不判断遮挡（可略过）
                        return isoverlapped(preWin, form);

                    if (winrect.X == 0 && winrect.Width == 54 && winrect.Height == 54) //开始按钮。不判断遮挡（可略过）
                        return isoverlapped(preWin, form);

                    var formRect = new Rectangle(form.Location, form.Size); //Form窗体矩形
                    if (formRect.IntersectsWith(winrect)) //判断是否遮挡
                        return true;
                }

                return isoverlapped(preWin, form);
            }
            catch
            {
            }

            return false;
        }

        private void ShowWindow()
        {
            CommonMethod.DetermineCall(this, delegate
            {
                this.ForceActivate();
                Update();
            });
        }

        private void SetTopMost(bool result)
        {
            TopMost = result;
        }

        private void pnlTop_Click(object sender, EventArgs e)
        {
            SetTopMost(!TopMost);
            CommonSetting.SetValue("窗体置顶", TopMost);
            BeginInvoke(new Action(() => ChangeThemeDelegate?.Invoke()));
        }

        private void pnlDark_Click(object sender, EventArgs e)
        {
            CommonSetting.夜间模式 = !CommonSetting.夜间模式;
            CommonSetting.SetValue("日夜间模式", CommonSetting.夜间模式 ? ThemeStyle.夜间模式.ToString() : ThemeStyle.日间模式.ToString());

            BeginInvoke(new Action(() => ChangeThemeDelegate?.Invoke()));
        }

        private void pnlSetting_Click(object sender, EventArgs e)
        {
            if (sender is Control ctrl) cmsNotify.Show(this, new Point(ctrl.Left, ctrl.Top + ctrl.Height));
        }

        private void tsmExit_Click(object sender, EventArgs e)
        {
            if (CommonSetting.记住窗口尺寸 && Size.IsValidate())
            {
                try
                {
                    var oriSize = new Size((int)(Size.Width / CommonTheme.DpiScale), (int)(Size.Height / CommonTheme.DpiScale));
                    if (oriSize.IsValidate())
                        CommonSetting.SetValue("上次窗口尺寸", oriSize);
                }
                catch { }
            }
            UnRegAllHandle();
            FrmTool.Hide();
            Hide();
            notifyMain.Dispose();
            CommonMethod.Exit();
        }

        private const int WM_HOTKEY = 0x312; //窗口消息-热键
        private const int WM_CREATE = 0x1; //窗口消息-创建
        private const int WM_DESTROY = 0x2; //窗口消息-销毁

        private bool IsOnSetting;

        public static List<HotKeyEntity> LstHotKeys = new List<HotKeyEntity>();
        private const ushort Hot_粘贴识别 = 0x3454;
        private const ushort Hot_粘贴翻译 = 0x3468;

        private const ushort Hot_文本识别 = 0x3455;
        private const ushort Hot_截图识别 = 0x3456;
        private const ushort Hot_竖排识别 = 0x3457;
        private const ushort Hot_公式识别 = 0x3458;
        private const ushort Hot_表格识别 = 0x3459;
        private const ushort Hot_截图翻译 = 0x3460;
        private const ushort Hot_截图搜索 = 0x3461;
        private const ushort Hot_文件识别 = 0x3479;
        private const ushort Hot_批量识别 = 0x3480;

        private const ushort Hot_截图贴图 = 0x3462;
        private const ushort Hot_快速贴图 = 0x3472;
        private const ushort Hot_显隐贴图 = 0x3473;
        private const ushort Hot_粘贴贴图 = 0x3463;

        private const ushort Hot_滚动截图 = 0x3465;
        private const ushort Hot_快速截图 = 0x3471;
        private const ushort Hot_上次截图 = 0x3487;

        private const ushort Hot_截图编辑 = 0x3466;
        private const ushort Hot_延时截图 = 0x3467;

        private const ushort Hot_活动窗口 = 0x3474;
        private const ushort Hot_活动显示器 = 0x3475;
        private const ushort Hot_全屏截图 = 0x3476;
        private const ushort Hot_固定区域截图 = 0x3477;
        private const ushort Hot_打开截图文件夹 = 0x3478;

        private const ushort Hot_固定区域识别 = 0x3469;
        private const ushort Hot_固定区域翻译 = 0x3470;

        private const ushort Hot_取色器 = 0x3481;
        private const ushort Hot_调色板 = 0x3482;
        private const ushort Hot_标尺 = 0x3483;
        private const ushort Hot_图片压缩 = 0x3484;
        private const ushort Hot_网络检测 = 0x3485;
        private const ushort Hot_文档矫正 = 0x3486;
        private const ushort Hot_放大镜 = 0x3488;
        private const ushort Hot_白板 = 0x3489;
        private const ushort Hot_PDF转图片 = 0x3490;
        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);
            switch (m.Msg)
            {
                case WM_HOTKEY: //窗口消息-热键ID
                    if (!IsOnSetting)
                    {
                        switch (m.WParam.ToInt32())
                        {
                            #region OCR相关

                            case Hot_截图识别:
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_文本识别:
                                InitItemTypeByValue(tsmContentType, OcrType.文本.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_竖排识别:
                                InitItemTypeByValue(tsmContentType, OcrType.竖排.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_公式识别:
                                InitItemTypeByValue(tsmContentType, OcrType.公式.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_表格识别:
                                InitItemTypeByValue(tsmContentType, OcrType.表格.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_截图翻译:
                                InitItemTypeByValue(tsmContentType, OcrType.翻译.GetHashCode().ToString());
                                截图识别ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_截图搜索:
                                截图识别ToolStripMenuItem_Click("搜索", null);
                                break;
                            case Hot_固定区域识别:
                                FixAreaItemClick(CommonEnumAction<string>.FindMeunItem(cmsNotify, "固定区域识别".CurrentText()));
                                break;
                            case Hot_固定区域翻译:
                                FixAreaItemClick(CommonEnumAction<string>.FindMeunItem(cmsNotify, "固定区域翻译".CurrentText()));
                                break;
                            case Hot_文件识别:
                                ToolStripMenuItem_Click(CommonEnumAction<string>.FindMeunItem(cmsNotify, "文件识别".CurrentText()), null);
                                break;
                            case Hot_批量识别:
                                批量识别ToolStripMenuItem_Click(null, null);
                                break;

                            #endregion

                            #region 截图

                            case Hot_快速截图:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.快速截图), null);
                                break;
                            case Hot_截图编辑:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图编辑), null);
                                break;
                            case Hot_上次截图:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.上次区域截图), null);
                                break;
                            case Hot_延时截图:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.延时截图), null);
                                break;
                            case Hot_滚动截图:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.滚动截屏), null);
                                break;

                            case Hot_活动窗口:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.活动窗口), null);
                                break;
                            case Hot_活动显示器:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.活动显示器), null);
                                break;
                            case Hot_全屏截图:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.全屏截图), null);
                                break;
                            case Hot_固定区域截图:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.固定区域截图), null);
                                break;
                            case Hot_打开截图文件夹:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.打开截图文件夹), null);
                                break;

                            #endregion

                            case Hot_粘贴翻译:
                                InitItemTypeByValue(tsmContentType, OcrType.翻译.GetHashCode().ToString());
                                CopyAction(true);
                                break;
                            case Hot_快速贴图:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.快速贴图), null);
                                break;
                            case Hot_截图贴图:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.截图贴图), null);
                                break;
                            case Hot_粘贴贴图:
                                文字贴图ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_显隐贴图:
                                ProcessCaptureClickAction(CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, CaptureActions.显隐贴图), null);
                                break;

                            case Hot_粘贴识别:
                                CopyAction();
                                break;

                            case Hot_取色器:
                                取色器ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_放大镜:
                                放大镜ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_白板:
                                白板ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_调色板:
                                调色板ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_标尺:
                                标尺工具ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_图片压缩:
                                批量压缩ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_PDF转图片:
                                PDF转图片ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_文档矫正:
                                批量矫正ToolStripMenuItem_Click(null, null);
                                break;
                            case Hot_网络检测:
                                pnlNetWork_Click(null, null);
                                break;
                        }
                    }
                    break;
                case WM_CREATE: //窗口消息-创建
                    CommonTheme.DpiScale = HighDpiHelper.GetFormDpi(this);
                    CommonTheme.BaseDpiScale = CommonTheme.DpiScale;
                    this.RefreshDpiScale();
                    InitScreenShot();
                    RegAll(true);
                    break;
                case WM_DESTROY: //窗口消息-销毁
                    UnRegAllHandle();
                    break;
            }
        }

        private void UnRegAllHandle()
        {
            try
            {
                UnRegAll();
            }
            catch
            {
            }
        }

        private void RegHotKey(List<HotKeyEntity> lstKeys)
        {
            foreach (var entity in lstKeys)
            {
                try
                {
                    UnRegHotKey(entity.Id);
                    HotKeyHelper.RegKey(Handle, entity);
                }
                catch { }
            }
        }

        private void RefreshMenuItemShortCur()
        {
            foreach (var entity in LstHotKeys)
            {
                try
                {
                    SetItemShortCut(entity);
                }
                catch { }
            }
        }

        private void SetItemShortCut(HotKeyEntity entity)
        {
            var item = CommonEnumAction<CaptureActions>.FindMeunItem(cmsNotify, entity.KeyName);
            if (item == null)
            {
                return;
            }
            item.ShowShortcutKeys = true;
            item.ShortcutKeyDisplayString = entity.StrKey?.Replace(" ", "").Trim();
            if (item.DropDownItems.Count > 0)
            {
                if (string.IsNullOrEmpty(item.Name))
                {
                    if (!string.IsNullOrEmpty(item.AccessibleDescription))
                        item.Text = string.Format("{0}    {1}", item.AccessibleDescription.CurrentText(), item.ShortcutKeyDisplayString).Trim();
                }
                else
                    item.Text = string.Format("{0}    {1}", item.Name.Substring(0, item.Name.ToLower().IndexOf("toolstrip")).CurrentText(), item.ShortcutKeyDisplayString).Trim();
            }
        }

        private void UnRegHotKey(int keyCode)
        {
            try
            {
                HotKeyHelper.UnRegKey(Handle, keyCode);
            }
            catch (Exception oe)
            {
                Log.WriteError("UnRegHotKey", oe);
            }
        }

        ////存放观察链中下一个窗口句柄   
        //IntPtr NextClipHwnd;

        //BlockingCollection<string> ClipboardTexts = new BlockingCollection<string>();

        //// SetClipboardViewer 用于往观察链中添加一个窗口句柄，这个窗口就可成为观察链中的一员了，返回值指向下一个观察者
        //[System.Runtime.InteropServices.DllImport("user32")]
        //private static extern IntPtr SetClipboardViewer(IntPtr hwnd);

        ////ChangeClipboardChain删除由hwnd指定的观察链成员，这是一个窗口句柄，第二个参数hWndNext是观察链中下一个窗口的句柄
        //[System.Runtime.InteropServices.DllImport("user32")]
        //private static extern IntPtr ChangeClipboardChain(IntPtr hwnd, IntPtr hWndNext);

        ////SendMessage 发送消息
        //[System.Runtime.InteropServices.DllImport("user32")]
        //private static extern int SendMessage(IntPtr hwnd, int wMsg, IntPtr wParam, IntPtr lParam);

        ////Clipboard内容变化消息
        //const int WM_DRAWCLIPBOARD = 0x308;
        ////Clipboard观察链变化消息
        //const int WM_CHANGECBCHAIN = 0x30D;

        ////private ClipboardListener ClipboardListener;
        ////public static ClipboardNative ClipboardNativeService;
        //private void RegisterClipboard()
        //{
        //    //获得观察链中下一个窗口句柄
        //    NextClipHwnd = SetClipboardViewer(this.Handle);
        //    //ClipboardNativeService = new ClipboardNative(new ActiveWindowListener());
        //    //ClipboardListener = new ClipboardListener(ClipboardNativeService);
        //    //ClipboardListener.开始箭头(true);
        //    FileStatusResultProcess();
        //}

        //private void UnRegisterClipboard()
        //{
        //    //从观察链中删除本观察窗口
        //    ChangeClipboardChain(this.Handle, NextClipHwnd);
        //    //将变动消息WM_CHANGECBCHAIN消息传递到下一个观察链中的窗口
        //    SendMessage(NextClipHwnd, WM_CHANGECBCHAIN, this.Handle, NextClipHwnd);
        //    //if (ClipboardListener != null)
        //    //{
        //    //    ClipboardListener.Stop();
        //    //    ClipboardListener.Dispose();
        //    //}
        //}

        private void tsmTrans_Click(object sender, EventArgs e)
        {
            var txt = GetCurrentTxt(false);
            TransByText(txt);
        }

        private void TransByText(string content)
        {
            if (!CommonString.IsOnRec && !string.IsNullOrEmpty(content))
            {
                SetPicImage(null);
                OcrPoolProcess.ProcessByText(NowOcrGroupType, CurrentTranslateFrom, CurrentTranslateTo,
                    content, ProcessBy.主界面, null);
            }
        }

        //private void FileStatusResultProcess()
        //{
        //    new Thread(p =>
        //    {
        //        try
        //        {
        //            foreach (var content in ClipboardTexts.GetConsumingEnumerable())
        //            {
        //                try
        //                {
        //                    //if (IsCopyTrans && NowOcrType == OCRType.翻译)
        //                    //{
        //                    //    TransByText(content);
        //                    //}
        //                    ////CodeProcessHelper.SetFileStatusResult(content);
        //                }
        //                catch (Exception oe)
        //                {
        //                    Console.WriteLine(oe.Message);
        //                }
        //            }
        //        }
        //        catch (Exception oe)
        //        {
        //            Console.WriteLine(oe.Message);
        //        }
        //    })
        //    { IsBackground = true, Priority = ThreadPriority.Highest }.开始箭头();
        //}

        private void IniRegKey(HotKeyEntity entity)
        {
            var strKey = CommonSetting.GetValue<string>("快捷键", entity.KeyName.Trim());
            if (string.IsNullOrEmpty(strKey))
            {
                entity.Hotkey = entity.DefaultKey;
            }
            else
            {
                if (BoxUtil.IsInt(strKey))
                {
                    entity.Hotkey = (Keys)BoxUtil.GetInt32FromObject(strKey);
                }
                else
                {
                    entity.Hotkey = CommonSetting.ConvertToEnum(strKey, entity.Hotkey);
                }
            }
        }

        private void InitHotKeyList()
        {
            #region 其他

            var txt固定区域截图 = new HotKeyEntity(Hot_固定区域截图)
            {
                KeyName = "固定区域截图",
                Desc = "固定区域截图",
                Group = HotKeyType.其他
            };
            IniRegKey(txt固定区域截图);
            LstHotKeys.Add(txt固定区域截图);
            var fixOcrEntity = new HotKeyEntity(Hot_固定区域识别)
            {
                KeyName = "固定区域识别",
                Desc = "固定区域截图并识别",
                Group = HotKeyType.其他
            };
            IniRegKey(fixOcrEntity);
            LstHotKeys.Add(fixOcrEntity);
            var fixTransEntity = new HotKeyEntity(Hot_固定区域翻译)
            {
                KeyName = "固定区域翻译",
                Desc = "固定区域截图并翻译",
                Group = HotKeyType.其他
            };
            IniRegKey(fixTransEntity);
            LstHotKeys.Add(fixTransEntity);

            var transEntity = new HotKeyEntity(Hot_截图翻译)
            {
                KeyName = "截图翻译",
                Desc = "截图并以【翻译】模式识别",
                Group = HotKeyType.其他
            };
            IniRegKey(transEntity);
            LstHotKeys.Add(transEntity);
            var transTextEntity = new HotKeyEntity(Hot_粘贴翻译)
            {
                KeyName = "粘贴翻译",
                Desc = "翻译粘贴板文字或图片",
                Group = HotKeyType.其他
            };
            IniRegKey(transTextEntity);
            LstHotKeys.Add(transTextEntity);
            var searchEntity = new HotKeyEntity(Hot_截图搜索)
            {
                KeyName = "截图搜索",
                Desc = "截图并搜索识别内容",
                Group = HotKeyType.其他
            };
            IniRegKey(searchEntity);
            LstHotKeys.Add(searchEntity);
            var 批量识别 = new HotKeyEntity(Hot_批量识别)
            {
                KeyName = "批量识别",
                Desc = "批量识别文件内容",
                Group = HotKeyType.其他
            };
            IniRegKey(批量识别);
            LstHotKeys.Add(批量识别);
            #endregion

            #region 工具
            var 取色器 = new HotKeyEntity(Hot_取色器)
            {
                KeyName = "取色器",
                Desc = "打开取色器进行屏幕取色",
                Group = HotKeyType.工具
            };
            IniRegKey(取色器);
            LstHotKeys.Add(取色器);
            var 放大镜 = new HotKeyEntity(Hot_放大镜)
            {
                KeyName = "放大镜",
                Desc = "打开放大镜工具",
                Group = HotKeyType.工具,
            };
            IniRegKey(放大镜);
            LstHotKeys.Add(放大镜);
            var 白板 = new HotKeyEntity(Hot_白板)
            {
                KeyName = "白板",
                Desc = "打开白板工具",
                Group = HotKeyType.工具,
            };
            IniRegKey(白板);
            LstHotKeys.Add(白板);
            var 调色板 = new HotKeyEntity(Hot_调色板)
            {
                KeyName = "调色板",
                Desc = "打开调色板工具",
                Group = HotKeyType.工具
            };
            IniRegKey(调色板);
            LstHotKeys.Add(调色板);
            var 标尺 = new HotKeyEntity(Hot_标尺)
            {
                KeyName = "标尺",
                Desc = "打开标尺工具",
                Group = HotKeyType.工具
            };
            IniRegKey(标尺);
            LstHotKeys.Add(标尺);
            var 图片压缩 = new HotKeyEntity(Hot_图片压缩)
            {
                KeyName = "图片压缩",
                Desc = "打开批量图片压缩工具",
                Group = HotKeyType.工具
            };
            IniRegKey(图片压缩);
            LstHotKeys.Add(图片压缩);
            var PDF转图片 = new HotKeyEntity(Hot_PDF转图片)
            {
                KeyName = "PDF转图片",
                Desc = "打开PDF转图片工具",
                Group = HotKeyType.工具
            };
            IniRegKey(PDF转图片);
            LstHotKeys.Add(PDF转图片);
            var 文档矫正 = new HotKeyEntity(Hot_文档矫正)
            {
                KeyName = "文档矫正",
                Desc = "打开批量文档矫正工具",
                Group = HotKeyType.工具
            };
            IniRegKey(文档矫正);
            LstHotKeys.Add(文档矫正);
            var 网络检测 = new HotKeyEntity(Hot_网络检测)
            {
                KeyName = "网络检测",
                Desc = "打开网络检测工具",
                Group = HotKeyType.工具
            };
            IniRegKey(网络检测);
            LstHotKeys.Add(网络检测);

            #endregion

            #region 贴图

            var fastPaste = new HotKeyEntity(Hot_快速贴图)
            {
                DefaultKey = Keys.F2,
                KeyName = "快速贴图",
                Desc = "截图（不带编辑）并贴图到屏幕上",
                Group = HotKeyType.贴图
            };
            IniRegKey(fastPaste);
            LstHotKeys.Add(fastPaste);
            var capturePaste = new HotKeyEntity(Hot_截图贴图)
            {
                DefaultKey = Keys.F7,
                KeyName = "截图贴图",
                Desc = "截图（带编辑）并贴图到屏幕上",
                Group = HotKeyType.贴图
            };
            IniRegKey(capturePaste);
            LstHotKeys.Add(capturePaste);
            var txtPaste = new HotKeyEntity(Hot_粘贴贴图)
            {
                DefaultKey = Keys.F6,
                KeyName = "粘贴贴图",
                Desc = "将粘贴板中的文字或图片贴到屏幕上",
                Group = HotKeyType.贴图
            };
            IniRegKey(txtPaste);
            LstHotKeys.Add(txtPaste);
            var hidePaste = new HotKeyEntity(Hot_显隐贴图)
            {
                KeyName = "显隐贴图",
                Desc = "显示/隐藏所有贴图",
                Group = HotKeyType.贴图
            };
            IniRegKey(hidePaste);
            LstHotKeys.Add(hidePaste);

            #endregion

            #region 截图
            var fastCapture = new HotKeyEntity(Hot_快速截图)
            {
                DefaultKey = Keys.Control | Keys.Alt | Keys.A,
                KeyName = "快速截图",
                Desc = "框选矩形截图（不带编辑）",
                Group = HotKeyType.截图
            };
            IniRegKey(fastCapture);
            LstHotKeys.Add(fastCapture);
            var freeCapture = new HotKeyEntity(Hot_截图编辑)
            {
                DefaultKey = Keys.PrintScreen,
                KeyName = "截图编辑",
                Desc = "框选矩形截图（带编辑）",
                Group = HotKeyType.截图
            };
            IniRegKey(freeCapture);
            LstHotKeys.Add(freeCapture);
            var lastCapture = new HotKeyEntity(Hot_上次截图)
            {
                KeyName = "上次截图",
                Desc = "上次区域截图（不带编辑）",
                Group = HotKeyType.截图
            };
            IniRegKey(lastCapture);
            LstHotKeys.Add(lastCapture);
            var txtGunDong = new HotKeyEntity(Hot_滚动截图)
            {
                KeyName = "滚动截图",
                Desc = "滚动截屏",
                Group = HotKeyType.截图
            };
            IniRegKey(txtGunDong);
            LstHotKeys.Add(txtGunDong);
            var txtDelay = new HotKeyEntity(Hot_延时截图)
            {
                KeyName = "延时截图",
                Desc = "延时截图",
                Group = HotKeyType.截图
            };
            IniRegKey(txtDelay);
            LstHotKeys.Add(txtDelay);

            var txt活动窗口 = new HotKeyEntity(Hot_活动窗口)
            {
                KeyName = "活动窗口",
                Desc = "活动窗口截图",
                Group = HotKeyType.截图
            };
            IniRegKey(txt活动窗口);
            LstHotKeys.Add(txt活动窗口);

            var txt全屏截图 = new HotKeyEntity(Hot_全屏截图)
            {
                KeyName = "全屏截图",
                Desc = "全屏截图",
                Group = HotKeyType.截图
            };
            IniRegKey(txt全屏截图);
            LstHotKeys.Add(txt全屏截图);

            var txt活动显示器 = new HotKeyEntity(Hot_活动显示器)
            {
                KeyName = "活动显示器",
                Desc = "活动显示器截图",
                Group = HotKeyType.截图
            };
            IniRegKey(txt活动显示器);
            LstHotKeys.Add(txt活动显示器);

            var txt打开截图文件夹 = new HotKeyEntity(Hot_打开截图文件夹)
            {
                KeyName = "截图文件夹",
                Desc = "打开截图文件夹",
                Group = HotKeyType.截图
            };
            IniRegKey(txt打开截图文件夹);
            LstHotKeys.Add(txt打开截图文件夹);

            #endregion

            #region 识别

            var captureEntity = new HotKeyEntity(Hot_截图识别)
            {
                DefaultKey = Keys.F3,
                KeyName = "截图识别",
                Desc = "截图并以【当前主界面中选择的模式】识别",
                Group = HotKeyType.识别
            };
            IniRegKey(captureEntity);
            LstHotKeys.Add(captureEntity);
            var 文件识别 = new HotKeyEntity(Hot_文件识别)
            {
                KeyName = "文件识别",
                Desc = "选择文件并识别文件内容",
                Group = HotKeyType.识别
            };
            IniRegKey(文件识别);
            LstHotKeys.Add(文件识别);
            var copyOcrEntity = new HotKeyEntity(Hot_粘贴识别)
            {
                KeyName = "粘贴识别",
                Desc = "粘贴并以【当前选择的模式】识别",
                Group = HotKeyType.识别
            };
            IniRegKey(copyOcrEntity);
            LstHotKeys.Add(copyOcrEntity);
            var ocrEntity = new HotKeyEntity(Hot_文本识别)
            {
                DefaultKey = Keys.F4,
                KeyName = "文本识别",
                Desc = "截图并以【文字】模式识别",
                Group = HotKeyType.识别
            };
            IniRegKey(ocrEntity);
            LstHotKeys.Add(ocrEntity);
            var shuEntity = new HotKeyEntity(Hot_竖排识别)
            {
                KeyName = "竖排识别",
                Desc = "截图并以【竖排文字】模式识别",
                Group = HotKeyType.识别
            };
            IniRegKey(shuEntity);
            LstHotKeys.Add(shuEntity);
            var mathEntity = new HotKeyEntity(Hot_公式识别)
            {
                KeyName = "公式识别",
                Desc = "截图并以【公式】模式识别",
                Group = HotKeyType.识别
            };
            IniRegKey(mathEntity);
            LstHotKeys.Add(mathEntity);
            var tableEntity = new HotKeyEntity(Hot_表格识别)
            {
                KeyName = "表格识别",
                Desc = "截图并以【表格】模式识别",
                Group = HotKeyType.识别
            };
            IniRegKey(tableEntity);
            LstHotKeys.Add(tableEntity);

            #endregion

            LstHotKeys.Reverse();
        }

        private void RegAll(bool isFirst)
        {
            if (LstHotKeys.Count <= 0) InitHotKeyList();
            try
            {
                IsOnSetting = true;
                RegHotKey(LstHotKeys);
                RefreshMenuItemShortCur();
            }
            catch (Exception oe)
            {
                Log.WriteError("RegAll", oe);
            }
            finally
            {
                IsOnSetting = false;
                if (isFirst) ShowNotifyMessage();
            }
        }

        private void ShowNotifyMessage()
        {
            if (!CommonSetting.启动时提示快捷键)
                return;
            var sb = new StringBuilder();
            foreach (var item in LstHotKeys)
                if (!string.IsNullOrEmpty(item.StrKey) && !Equals(item.StrKey, CommonString.StrDefaultDesc))
                    sb.AppendLine(string.Format("{0}:{1}", item.KeyName.CurrentText(), item.StrKey));
            var strMsg = string.Empty;
            if (sb.Length <= 0)
                strMsg = "当前无快捷键。".CurrentText();
            else
                strMsg = string.Format("【当前已设置快捷键】".CurrentText() + "\n{0}", sb);
            strMsg += "\n\n" + "更多快捷键，请在\n系统设置->快捷键 中设置！".CurrentText();
            CommonMethod.ShowNotificationTip(strMsg, null, 15 * 1000);
            //notifyMain.ShowBalloonTip(5000, Application.ProductName, string.Format("小主!我在这里，有事双击我！\n\n{0}\n\n", strMsg), ToolTipIcon.Info);
        }

        private void UnRegAll()
        {
            foreach (var item in LstHotKeys) UnRegHotKey(item.Id);
            //UnRegHotKey(StrKey_Trans);
        }

        public static void ShowReort()
        {
            CommonMethod.ShowHelpMsg("感谢您的反馈，助手的发展离不开您的支持！".CurrentText());
            var report = new FrmReport
            {
                Icon = FrmTool.Icon,
                Text = "问题反馈".CurrentText()
            };
            report.TopMost = true;
            report.StartPosition = FormStartPosition.CenterScreen;
            report.Show();
        }

        private void 问题反馈ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            FrmMain.ShowReort();
        }

        private void 系统设置SToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var lastDpi = CommonSetting.跟随系统DPI缩放;
            using (var frmSetting = new FormSetting())
            {
                frmSetting.Icon = Icon;
                //if (this.Visible)
                //    frmSetting.TopMost = true;
                if (!Visible) frmSetting.StartPosition = FormStartPosition.CenterScreen;
                if (Equals(sender, tsmToolImage))
                {
                    frmSetting.OpenTab = "界面";
                    frmSetting.SharkGroup = "工具栏样式";
                }
                //else if (Equals(sender, 关于我们ToolStripMenuItem))
                //{
                //    frmSetting.OpenTab = "关于";
                //    frmSetting.SharkGroup = "联系我们";
                //}
                else
                {
                    var item = sender as ToolStripMenuItem;
                    var strTmp = item?.AccessibleDescription;
                    if (!string.IsNullOrEmpty(strTmp) && strTmp.Contains("配置"))
                    {
                        frmSetting.OpenTab = strTmp.Replace("配置", "").Trim();
                    }
                }

                frmSetting.ShowDialog(this);
            }

            UnRegAll();

            SetTopMost(CommonSetting.窗体置顶);

            InitItemTypeByValue(tsmPicViewModel, CommonSetting.展示模式);

            var spiltMode = CommonSetting.ConvertToEnum(CommonSetting.分段模式, NowSpiltModel);
            if (!Equals(NowSpiltModel, spiltMode))
            {
                BindSpiltModel(spiltMode);
            }

            CommonUpdate.InitUpdate();

            InitThemeTask();

            //SetTheme();
            //CommonMsg.Refresh();

            NowOcrGroupType = OcrHelper.GetGroupByName(CommonSetting.识别引擎);

            RefreshUcContent();

            RegAll(false);

            SetSearchEngine();

            NowLoadingType = CommonSetting.ConvertToEnum(CommonSetting.加载动画, NowLoadingType);

            try
            {
                if (!Equals(lastDpi, CommonSetting.跟随系统DPI缩放))
                {
                    CommonString.SetScaleModel();

                    HighDpiHelper.AdjustControlImagesDpiScale(this, CommonString.CommonGraphicsUnit() == GraphicsUnit.Pixel);

                    OnThemeChange();
                }
            }
            catch { }

            if (Visible) ShowWindow();

            LocalOcrService.CloseService();
            if (CommonSetting.启用本地识别)
            {
                Task.Factory.StartNew(() =>
                {
                    LocalOcrService.OpenOcrService((int)CommonSetting.本地识别端口, (int)CommonSetting.本地识别线程数, false);
                });
            }
        }

        #endregion

        #region 登录相关

        private void InitNetWorkInfo()
        {
            SetNowNetWork(CommonString.IsOnLine, true);
            NetworkChange.NetworkAvailabilityChanged += NetworkChange_NetworkAvailabilityChanged;
            IpInitFinishEvent += DoLoginSilence;
        }

        private void NetworkChange_NetworkAvailabilityChanged(object sender, NetworkAvailabilityEventArgs e)
        {
            SetNowNetWork(e.IsAvailable);
        }

        private void SetNowNetWork(bool isAvailable, bool isUserChange = false)
        {
            CommonString.IsOnLine = isAvailable;
            if (!isUserChange && isAvailable)
            {
                DnsHelper.Init();
            }
            if (isUserChange)
                CommonString.IsOnLine = NetworkInterface.GetIsNetworkAvailable();
            else
                NetWorkChangeEvent();
            //tipMain.SetToolTip(pnlNetWork, "本地网络" + (CommonString.IsOnLine ? "正常" : "异常"));
            //lnkLogin.Image = CommonString.IsOnLine ? Properties.Resources.Info_OK : Properties.Resources.Info_Error;
        }

        public static EventHandler IpInitFinishEvent;
        private bool isFirstCheckLoginStatus = true;

        private void NetWorkChangeEvent()
        {
            DoLoginSilence(null, null);
        }

        private void DoLoginSilence(object sender, EventArgs e)
        {
            if (!CommonString.IsAutoLogin || Program.IsLogined())
            {
                return;
            }
            if (!string.IsNullOrEmpty(CommonSetting.用户名) && !string.IsNullOrEmpty(CommonSetting.密码))
            {
                var strMsg = "";
                var result = OcrHelper.DoLogin(CommonSetting.用户名, CommonSetting.密码, ref strMsg);
            }
            else
            {
                //未注册用户第一次自动Token续期
                if (isFirstCheckLoginStatus)
                {
                    isFirstCheckLoginStatus = false;
                    CheckLoginStatus();
                }
            }
        }

        private bool isOnBindUserInfo = false;

        private void BindUserInfo()
        {
            lock (StrToLoginStr)
            {
                if (!isOnBindUserInfo)
                {
                    isOnBindUserInfo = true;
                }
                else
                {
                    return;
                }
            }
            var lastText = LoginInfo.Text;
            var strText = StrToLoginStr.CurrentText();
            try
            {
                Bitmap image = null;
                if (!Program.IsLogined())
                {
                    LoginInfo.AccessibleDescription = StrToLoginStr;
                    tipMain.SetToolTip(LoginInfo, "点击登录/注册账号   \n注册即送体验会员     \n限时VIP优惠进行中…".CurrentText());
                }
                else
                {
                    LoginInfo.AccessibleDescription = "";
                    strText = Program.NowUser.NickName;
                    image = LoginInfo.SetResourceImage("vip_" + Program.NowUser.UserType);
                    var tipInfo = string.Format("{0}-{1}"
                        , string.IsNullOrEmpty(Program.NowUser.NickName)
                            ? Program.NowUser.Account
                            : Program.NowUser.NickName
                        , Program.NowUser.UserTypeName.CurrentText()
                    );
                    if (!string.IsNullOrEmpty(Program.NowUser.VipMsg))
                    {
                        tipInfo += "\n" + Program.NowUser.VipMsg;
                    }
                    tipMain.SetToolTip(LoginInfo, tipInfo);
                }
                if (image == null)
                {
                    image = LoginInfo.SetResourceImage("qqKeFu");
                }
                BeginInvoke(new Action(() =>
                {
                    LoginInfo.Text = strText;
                    try
                    {
                        LoginInfo.Image = ImageProcessHelper.ScaleImage(new Bitmap(image), CommonTheme.DpiScale);
                    }
                    catch { }
                    ProcessForbidControls();
                }));
            }
            catch (Exception oe)
            {
                Log.WriteError("BindUserInfo", oe);
            }
            finally
            {
                isOnBindUserInfo = false;
                if (!Equals(lastText, strText))
                {
                    CheckLoginStatus();
                }
            }
        }

        #endregion
    }
}