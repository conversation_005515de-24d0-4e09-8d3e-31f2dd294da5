﻿using MetroFramework.Forms;
using OCRTools.Common;
using OCRTools.Language;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Reflection;
using System.Security.Permissions;
using System.Threading.Tasks;
using System.Web;
using System.Windows.Forms;

namespace OCRTools
{
    [PermissionSet(SecurityAction.Demand, Name = "FullTrust")]
    public partial class FrmGoBuy : MetroForm
    {
        private ChargeViewToUser _nowSelectedChargeType;

        private UserType _nowSelectedType;

        public UserTypeInfo NextUserType;

        public FrmGoBuy()
        {
            InitializeComponent();
            ShadowType = CommonString.CommonShadowType;

            this.AddContactUserBtn("FrmGoBuy");
            Shown += (sender, e) =>
            {
                InitSrcMsg();
            };
            var dtStart = DateTime.Now;
            this.FormClosing += (sender, e) =>
            {
                Log.TrackEvent("BuyForm:" + new TimeSpan(DateTime.Now.Ticks - dtStart.Ticks).TotalSeconds.ToString("F0") + "s");
            };
        }

        private async void btnOpenVip_Click(object sender, EventArgs e)
        {
            if (!Program.IsLogined())
            {
                CommonMethod.ShowHelpMsg("请登录后重试！".CurrentText());
                return;
            }

            if (!CommonString.IsOnLine)
            {
                CommonMethod.ShowHelpMsg(CommonString.StrNetWorkError.CurrentText());
                return;
            }

            if (_nowSelectedType == null)
            {
                CommonMethod.ShowHelpMsg("请选择要升级的类型！".CurrentText());
                return;
            }

            //if (NowSelectedType.Type == Program.NowUser?.UserType)
            //{
            //    CommonMethod.ShowHelpMsg(string.Format("当前已经是{0}，祝您使用愉快！", NowSelectedType.Type.ToString()));
            //    return;
            //}
            Top -= (785 - Height) / 2;
            Height = 785;

            var url = CommonString.HostAccount?.FullUrl + "code.aspx?op=pay&remark=" +
                      HttpUtility.UrlEncode(_nowSelectedChargeType?.Name +
                                            (_nowSelectedType.Name ?? _nowSelectedType.Type.ToString()))
                      + "&account=" + Program.NowUser.Account + "&lang=" + LanguageHelper.NowLanguage + "&from=1";

            try
            {
                btnOpenVip.Enabled = false;

                var html = await Task.Run(async () =>
                {
                    string result = "";
                    for (int i = 0; i < 5; i++)
                    {
                        result = WebClientExt.GetHtml(url, 30);
                        if (result.Trim().StartsWith("http"))
                        {
                            break;
                        }
                        await Task.Delay(500);
                    }
                    return result;
                });

                pnlMain.Controls.Clear();
                if (html.Trim().StartsWith("http"))
                {
                    var dicCheck = new Dictionary<string, string> { { "minute_show", "0分" } };
                    CommonMethod.LoadHtml(pnlMain, new Point(0, 0), Size, html.Trim(), dicCheck);
                    CommonMethod.ShowHelpMsg("支付完成后，如未自动开通，请发送付款截图给客服，祝您使用愉快！".CurrentText(), 10000);
                }
                else
                {
                    CommonMethod.ShowHelpMsg(CommonString.StrNetWorkError.CurrentText());
                }
            }
            finally
            {
                btnOpenVip.Enabled = true;
            }
        }

        private void FrmGoBuy_Load(object sender, EventArgs e)
        {
            // LoadInfo
            CommonUser.GetUserTypes(true)?.ForEach(p =>
            {
                var radioButton = new RadioButton
                {
                    Text = (p.Name ?? p.Type.ToString()).CurrentText(),
                    Tag = p,
                    AutoSize = true,
                    Font = CommonString.GetSysNormalFont(16F),
                    TabStop = false
                };
                CommonMethod.SetStyle(radioButton, ControlStyles.Selectable, false);
                radioButton.CheckedChanged += RadioButton_CheckedChanged;
                var image = radioButton.SetResourceImage("vip_" + p.Type) ?? radioButton.SetResourceImage("qqKeFu");
                if (image != null)
                {
                    radioButton.TextImageRelation = TextImageRelation.ImageBeforeText;
                    try
                    {
                        radioButton.Image = ImageProcessHelper.ScaleImage(image, CommonTheme.DpiScale);
                    }
                    catch { }
                }
                if (Equals(p.Type, NextUserType?.Code)) radioButton.Checked = true;
                pnlUserType.Controls.Add(radioButton);
            });
            if (pnlPayType.Controls.Count <= 0 && pnlUserType.Controls.Count > 0)
            {
                ((RadioButton)pnlUserType.Controls[0]).Checked = true;
            }
            var url = CommonString.HostAccount?.FullUrl + "Desc.aspx?type=" + (Program.NowUser?.UserType ?? 0) + "&lang=" + LanguageHelper.NowLanguage;
            CommonMethod.LoadHtml(pnlMain, new Point(7, 88), new Size(555, 390), url);
        }

        private void RadioButton_CheckedChanged(object sender, EventArgs e)
        {
            if (sender == null) return;
            _nowSelectedType = (sender as RadioButton)?.Tag as UserType;
            pnlPayType.Controls.Clear();
            _nowSelectedChargeType = null;
            _nowSelectedType?.UserChargeType?.ForEach(p =>
            {
                var radio = new RadioButton
                {
                    Text = p.Name.CurrentText(),
                    AutoSize = true,
                    Tag = p,
                    Font = CommonString.GetSysNormalFont(16F),
                    TabStop = false
                };
                CommonMethod.SetStyle(radio, ControlStyles.Selectable, false);
                radio.CheckedChanged += rdoByYear_CheckedChanged;
                if (!string.IsNullOrEmpty(p.Tag))
                {
                    radio.TextImageRelation = TextImageRelation.TextBeforeImage;
                    try
                    {
                        var image = ImageProcessHelper.ScaleImage(radio.SetResourceImage(p.Tag), CommonTheme.DpiScale);
                        image = ImageProcessHelper.ResizeImage(image, new Size(21, 21), true, true);
                        radio.Image = image;
                    }
                    catch { }
                }

                pnlPayType.Controls.Add(radio);
                if (p.IsDefault) radio.Checked = true;
            });
        }

        string yearTypeKey = "一二三四五六七八九十";
        string strYouHuiInfo = "";
        private void rdoByYear_CheckedChanged(object sender, EventArgs e)
        {
            var rdo = sender as RadioButton;
            if (rdo == null || !rdo.Checked)
                return;
            _nowSelectedChargeType = rdo.Tag as ChargeViewToUser;
            btnOpenVip.Text = _nowSelectedChargeType?.Desc.CurrentText(true) ?? "-";

            var image = btnOpenVip.SetResourceImage("vip_" + _nowSelectedType.Type) ?? btnOpenVip.SetResourceImage("qqKeFu");
            image = ImageProcessHelper.ScaleImage(image, CommonTheme.DpiScale);
            btnOpenVip.ImageSize = image.Size;
            btnOpenVip.Image = image;

            if (_nowSelectedChargeType != null)
            {
                var discountOriPrice = Math.Min(_nowSelectedChargeType.Price / _nowSelectedChargeType.OriPrice, _nowSelectedType.YearDiscount);
                var lstDesc = new List<string>();
                var monthPrice = 0d;
                var strYear = CommonMethod.SubString(_nowSelectedChargeType.Name, "", "年").Replace("两", "二");
                if (!string.IsNullOrEmpty(strYear) && yearTypeKey.Contains(strYear))
                {
                    monthPrice = Math.Floor(_nowSelectedChargeType.Price / (12 * (yearTypeKey.IndexOf(strYear) + 1)));
                }
                if (monthPrice > 0)
                {
                    lstDesc.Add(string.Format("约{0}元/月".CurrentText(), monthPrice.ToString("F0")));
                }
                lstDesc.Add(string.Format("限时{0}折优惠".CurrentText(), (discountOriPrice * 10).ToString("F0")));
                lstDesc.Add(string.Format("原价{0}元".CurrentText(), _nowSelectedChargeType.OriPrice.ToString("F0")));
                strYouHuiInfo = string.Join(",", lstDesc).Trim();
            }

            InitSrcMsg();
        }

        private void InitSrcMsg()
        {
            var loc = new Point(btnOpenVip.Right + 10, btnOpenVip.Top);
            var size = new Size(Width - btnOpenVip.Right, btnOpenVip.Height);
            ScrollMsg.ShowToWindow(btnOpenVip.Parent, strYouHuiInfo, loc, size);
        }
    }

    [Obfuscation]
    public class UserType
    {
        [Obfuscation] public int Type { get; set; }

        [Obfuscation] public string Name { get; set; }

        [Obfuscation] public double YearDiscount { get; set; }

        [Obfuscation] public List<ChargeViewToUser> UserChargeType { get; set; }
    }

    [Obfuscation]
    public class ChargeViewToUser
    {
        [Obfuscation] public string Name { get; set; }

        [Obfuscation] public string Desc { get; set; }

        [Obfuscation] public double OriPrice { get; set; }

        [Obfuscation] public double Price { get; set; }

        [Obfuscation] public bool IsDefault { get; set; }

        [Obfuscation] public string Tag { get; set; }
    }

    [Obfuscation]
    public class UserTypeInfo
    {
        public string Name { get; set; }

        public int Code { get; set; }
    }
}